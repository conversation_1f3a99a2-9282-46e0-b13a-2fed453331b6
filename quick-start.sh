#!/bin/bash

# Java CPU Monitor Agent - 快速开始脚本
# 这个脚本帮助用户快速体验CPU监控功能

set -e

echo "=========================================="
echo "🚀 Java CPU Monitor Agent 快速开始"
echo "=========================================="
echo ""

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境，请先安装Java 8+"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1-2)
echo "✅ Java环境: $JAVA_VERSION"

# 检查是否已构建
AGENT_JAR="target/java-cpu-monitor-agent-1.0.0.jar"
if [ ! -f "$AGENT_JAR" ]; then
    echo ""
    echo "📦 正在构建Agent..."
    if [ -f "build.sh" ]; then
        ./build.sh
    else
        mvn clean package -q
    fi
    echo "✅ 构建完成"
fi

echo ""
echo "🎯 选择体验方式:"
echo "1) 运行内置演示 (推荐新手)"
echo "2) 监控现有Java进程"
echo "3) 查看使用帮助"
echo ""

read -p "请选择 (1-3): " choice

case $choice in
    1)
        echo ""
        echo "🎬 启动演示应用..."
        echo "提示: 按 Ctrl+C 停止演示"
        echo ""
        sleep 2
        
        if [ -f "run-demo.sh" ]; then
            ./run-demo.sh
        else
            java -javaagent:$AGENT_JAR=interval=3000,threshold=50,topMethods=10 \
                 -cp target/test-classes \
                 com.monitor.agent.test.TestApplication
        fi
        ;;
    2)
        echo ""
        echo "📋 当前Java进程:"
        jps
        echo ""
        read -p "请输入要监控的进程ID: " pid
        
        if [[ "$pid" =~ ^[0-9]+$ ]]; then
            echo "🔗 正在附加到进程 $pid..."
            java -jar $AGENT_JAR $pid
        else
            echo "❌ 无效的进程ID"
            exit 1
        fi
        ;;
    3)
        echo ""
        echo "📖 使用帮助:"
        echo ""
        echo "监控您的Java应用:"
        echo "  java -javaagent:$AGENT_JAR -jar your-app.jar"
        echo ""
        echo "自定义配置:"
        echo "  java -javaagent:$AGENT_JAR=interval=5000,threshold=70 -jar your-app.jar"
        echo ""
        echo "参数说明:"
        echo "  interval  - 采样间隔(毫秒), 默认5000"
        echo "  threshold - CPU阈值(%), 默认70"
        echo "  topMethods - 显示热点方法数量, 默认10"
        echo ""
        echo "更多信息请查看 README.md"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "🎉 感谢使用 Java CPU Monitor Agent!"
echo "📚 更多功能请查看: README.md"
echo "🐛 问题反馈: https://github.com/helloworld9999/java-cpu-monitor-agent/issues"
