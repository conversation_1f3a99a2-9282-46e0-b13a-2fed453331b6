#!/bin/bash

# Docker容器启动脚本 - 集成CPU Monitor Agent

set -e

echo "=========================================="
echo "Java CPU Monitor Agent - Docker启动"
echo "=========================================="

# 显示环境信息
echo "Java版本: $(java -version 2>&1 | head -n1)"
echo "容器内存限制: $(cat /sys/fs/cgroup/memory/memory.limit_in_bytes 2>/dev/null || echo '未限制')"
echo "CPU核心数: $(nproc)"

# 检查Agent文件
AGENT_JAR="/opt/java-cpu-monitor-agent.jar"
if [ ! -f "$AGENT_JAR" ]; then
    echo "❌ 错误: Agent JAR文件不存在: $AGENT_JAR"
    exit 1
fi

echo "✅ Agent JAR文件: $AGENT_JAR"
echo "Agent配置: $AGENT_OPTS"

# 构建完整的Java命令
FULL_JAVA_OPTS="$JVM_MEMORY $JAVA_OPTS"

# 添加Docker容器特定的JVM参数
FULL_JAVA_OPTS="$FULL_JAVA_OPTS -XX:+UnlockExperimentalVMOptions"
FULL_JAVA_OPTS="$FULL_JAVA_OPTS -XX:+UseCGroupMemoryLimitForHeap"
FULL_JAVA_OPTS="$FULL_JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"

# 添加Agent参数
AGENT_PARAM="-javaagent:$AGENT_JAR=$AGENT_OPTS"

# 添加Java 11模块访问权限
MODULE_OPTS="--add-opens java.management/sun.management=ALL-UNNAMED"
MODULE_OPTS="$MODULE_OPTS --add-opens java.base/jdk.internal.misc=ALL-UNNAMED"

# 设置监控输出目录
export MONITOR_OUTPUT_DIR="/app/monitoring"
export MONITOR_REPORTS_DIR="/app/monitoring/reports"
export MONITOR_LOGS_DIR="/app/monitoring/logs"

# 创建监控目录
mkdir -p "$MONITOR_REPORTS_DIR" "$MONITOR_LOGS_DIR"

# 设置日志文件
LOG_FILE="$MONITOR_LOGS_DIR/cpu-monitor-$(date +%Y%m%d-%H%M%S).log"

echo "监控数据目录: $MONITOR_OUTPUT_DIR"
echo "监控报告目录: $MONITOR_REPORTS_DIR"
echo "监控日志文件: $LOG_FILE"

# 显示完整的启动命令
echo ""
echo "启动命令:"
echo "java $MODULE_OPTS $FULL_JAVA_OPTS $AGENT_PARAM $@"
echo ""

# 启动应用并重定向Agent输出到日志文件
exec java $MODULE_OPTS $FULL_JAVA_OPTS $AGENT_PARAM "$@" 2>&1 | tee "$LOG_FILE"
