#!/bin/bash

# 项目完整性检查脚本

set -e

echo "=========================================="
echo "Java CPU Monitor Agent - 项目检查"
echo "=========================================="

# 检查必要文件
echo "📁 检查项目文件..."

REQUIRED_FILES=(
    "pom.xml"
    "README.md"
    "LICENSE"
    "CHANGELOG.md"
    "CONTRIBUTING.md"
    "DOCKER_DEPLOYMENT_GUIDE.md"
    ".gitignore"
    "build.sh"
    "build-java11.sh"
    "run-demo.sh"
    "attach-agent.sh"
    "monitor-existing-container.sh"
    "git-setup.sh"
    "docker-compose.yml"
    "Dockerfile.with-agent"
    "docker-entrypoint.sh"
)

MISSING_FILES=()

for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file"
        MISSING_FILES+=("$file")
    fi
done

# 检查源代码目录
echo ""
echo "📂 检查源代码结构..."

SOURCE_DIRS=(
    "src/main/java/com/monitor/agent"
    "src/main/java/com/monitor/agent/profiler"
    "src/main/java/com/monitor/agent/instrumentation"
    "src/main/java/com/monitor/agent/collector"
    "src/main/java/com/monitor/agent/reporter"
    "src/test/java/com/monitor/agent/test"
    ".github/workflows"
    ".github/ISSUE_TEMPLATE"
)

for dir in "${SOURCE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir/"
    else
        echo "❌ $dir/"
        MISSING_FILES+=("$dir/")
    fi
done

# 检查Java源文件
echo ""
echo "☕ 检查Java源文件..."

JAVA_FILES=(
    "src/main/java/com/monitor/agent/CpuMonitorAgent.java"
    "src/main/java/com/monitor/agent/profiler/CpuProfiler.java"
    "src/main/java/com/monitor/agent/profiler/CpuMetrics.java"
    "src/main/java/com/monitor/agent/instrumentation/ClassTransformer.java"
    "src/main/java/com/monitor/agent/collector/DataCollector.java"
    "src/main/java/com/monitor/agent/reporter/ReportGenerator.java"
    "src/main/java/com/monitor/agent/reporter/ConsoleReporter.java"
    "src/test/java/com/monitor/agent/test/TestApplication.java"
)

for file in "${JAVA_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file"
        MISSING_FILES+=("$file")
    fi
done

# 检查脚本权限
echo ""
echo "🔧 检查脚本权限..."

SCRIPTS=(
    "build.sh"
    "build-java11.sh"
    "run-demo.sh"
    "attach-agent.sh"
    "monitor-existing-container.sh"
    "git-setup.sh"
    "check-project.sh"
    "docker-entrypoint.sh"
)

for script in "${SCRIPTS[@]}"; do
    if [ -f "$script" ]; then
        if [ -x "$script" ]; then
            echo "✅ $script (可执行)"
        else
            echo "⚠️ $script (不可执行)"
            chmod +x "$script"
            echo "   已修复权限"
        fi
    fi
done

# 检查Maven配置
echo ""
echo "📦 检查Maven配置..."

if [ -f "pom.xml" ]; then
    echo "✅ pom.xml存在"
    
    # 检查关键配置
    if grep -q "java-cpu-monitor-agent" pom.xml; then
        echo "✅ 项目名称配置正确"
    else
        echo "⚠️ 项目名称可能需要检查"
    fi
    
    if grep -q "ByteBuddy" pom.xml || grep -q "byte-buddy" pom.xml; then
        echo "✅ ByteBuddy依赖配置正确"
    else
        echo "⚠️ ByteBuddy依赖可能缺失"
    fi
    
    if grep -q "jackson" pom.xml; then
        echo "✅ Jackson依赖配置正确"
    else
        echo "⚠️ Jackson依赖可能缺失"
    fi
fi

# 检查Git配置
echo ""
echo "🔄 检查Git状态..."

if [ -d ".git" ]; then
    echo "✅ Git仓库已初始化"
    
    # 检查远程仓库
    if git remote -v >/dev/null 2>&1; then
        echo "✅ 远程仓库已配置:"
        git remote -v
    else
        echo "⚠️ 未配置远程仓库"
    fi
    
    # 检查分支
    CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "未知")
    echo "当前分支: $CURRENT_BRANCH"
    
    # 检查未提交的更改
    if git diff --quiet && git diff --cached --quiet; then
        echo "✅ 工作目录干净"
    else
        echo "⚠️ 有未提交的更改"
        echo "未暂存的文件:"
        git diff --name-only
        echo "已暂存的文件:"
        git diff --cached --name-only
    fi
else
    echo "❌ Git仓库未初始化"
    echo "运行 ./git-setup.sh 来初始化"
fi

# 检查构建状态
echo ""
echo "🔨 检查构建状态..."

if [ -d "target" ]; then
    echo "✅ target目录存在"
    
    if [ -f "target/java-cpu-monitor-agent-1.0.0.jar" ]; then
        echo "✅ Agent JAR文件已构建"
        echo "   文件大小: $(du -h target/java-cpu-monitor-agent-1.0.0.jar | cut -f1)"
    else
        echo "⚠️ Agent JAR文件未构建"
        echo "   运行 ./build-java11.sh 来构建"
    fi
    
    if [ -d "target/classes" ]; then
        echo "✅ 类文件已编译"
    else
        echo "⚠️ 类文件未编译"
    fi
else
    echo "⚠️ target目录不存在，项目未构建"
    echo "   运行 ./build-java11.sh 来构建项目"
fi

# 总结
echo ""
echo "=========================================="
echo "检查总结"
echo "=========================================="

if [ ${#MISSING_FILES[@]} -eq 0 ]; then
    echo "🎉 所有必要文件都存在！"
else
    echo "❌ 缺失以下文件:"
    for file in "${MISSING_FILES[@]}"; do
        echo "   - $file"
    done
fi

echo ""
echo "📋 下一步建议:"

if [ ! -d ".git" ]; then
    echo "1. 运行 ./git-setup.sh 初始化Git仓库"
fi

if [ ! -f "target/java-cpu-monitor-agent-1.0.0.jar" ]; then
    echo "2. 运行 ./build-java11.sh 构建项目"
fi

if [ -d ".git" ] && ! git remote -v >/dev/null 2>&1; then
    echo "3. 在GitHub上创建仓库并配置远程地址"
fi

echo "4. 测试Agent功能: ./run-demo.sh"
echo "5. 查看Docker部署指南: DOCKER_DEPLOYMENT_GUIDE.md"

echo ""
echo "🚀 准备发布到GitHub:"
echo "   - 确保所有文件都已提交"
echo "   - 创建GitHub仓库"
echo "   - 推送代码并创建发布版本"

echo ""
echo "检查完成！"
