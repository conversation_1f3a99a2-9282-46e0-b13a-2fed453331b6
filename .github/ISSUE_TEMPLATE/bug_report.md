---
name: Bug报告
about: 创建一个报告来帮助我们改进
title: '[BUG] '
labels: 'bug'
assignees: ''

---

**Bug描述**
清晰简洁地描述这个bug是什么。

**复现步骤**
复现该行为的步骤：
1. 执行 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

**预期行为**
清晰简洁地描述您期望发生什么。

**实际行为**
清晰简洁地描述实际发生了什么。

**截图**
如果适用，添加截图来帮助解释您的问题。

**环境信息：**
 - 操作系统: [例如 Ubuntu 20.04, Windows 10, macOS 12]
 - Java版本: [例如 Java 11, Java 17]
 - Agent版本: [例如 v1.0.0]
 - 容器环境: [例如 Docker, Kubernetes, 无]
 - JVM参数: [例如 -Xmx2g -XX:+UseG1GC]

**Agent配置**
```
interval=5000,threshold=70,topMethods=10
```

**日志输出**
```
粘贴相关的日志输出
```

**附加上下文**
在此处添加有关该问题的任何其他上下文。

**检查清单**
- [ ] 我已经搜索了现有的issues
- [ ] 我已经阅读了文档
- [ ] 我已经尝试了最新版本
- [ ] 我已经提供了完整的环境信息
