name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  test:
    name: Test on Java ${{ matrix.java }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: [8, 11, 17, 21]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK ${{ matrix.java }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ matrix.java }}
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Run tests
      run: mvn clean test
      
    - name: Build project
      run: mvn clean package -DskipTests
      
    - name: Verify JAR file
      run: |
        ls -la target/
        java -jar target/java-cpu-monitor-agent-1.0.0.jar --help || echo "Agent JAR created successfully"

  build-docker:
    name: Build Docker Image
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 11
      uses: actions/setup-java@v4
      with:
        java-version: '11'
        distribution: 'temurin'
        
    - name: Build Agent
      run: ./build-java11.sh
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build Docker image
      run: |
        docker build -f Dockerfile.with-agent -t java-cpu-monitor-agent:latest .
        docker images
        
    - name: Test Docker image
      run: |
        docker run --rm java-cpu-monitor-agent:latest java -version

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 11
      uses: actions/setup-java@v4
      with:
        java-version: '11'
        distribution: 'temurin'
        
    - name: Run OWASP Dependency Check
      run: |
        mvn org.owasp:dependency-check-maven:check
      continue-on-error: true
      
    - name: Upload security scan results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-scan-results
        path: target/dependency-check-report.html

  release:
    name: Create Release
    runs-on: ubuntu-latest
    needs: [test, build-docker, security-scan]
    if: github.event_name == 'release'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK 11
      uses: actions/setup-java@v4
      with:
        java-version: '11'
        distribution: 'temurin'
        
    - name: Build release artifacts
      run: |
        ./build-java11.sh
        cp target/java-cpu-monitor-agent-1.0.0.jar java-cpu-monitor-agent.jar
        
    - name: Create release archive
      run: |
        tar -czf java-cpu-monitor-agent-${{ github.event.release.tag_name }}.tar.gz \
          java-cpu-monitor-agent.jar \
          README.md \
          DOCKER_DEPLOYMENT_GUIDE.md \
          build.sh \
          build-java11.sh \
          run-demo.sh \
          attach-agent.sh \
          monitor-existing-container.sh \
          docker-compose.yml \
          Dockerfile.with-agent \
          docker-entrypoint.sh
          
    - name: Upload release assets
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./java-cpu-monitor-agent-${{ github.event.release.tag_name }}.tar.gz
        asset_name: java-cpu-monitor-agent-${{ github.event.release.tag_name }}.tar.gz
        asset_content_type: application/gzip
        
    - name: Upload JAR file
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ github.event.release.upload_url }}
        asset_path: ./java-cpu-monitor-agent.jar
        asset_name: java-cpu-monitor-agent.jar
        asset_content_type: application/java-archive
