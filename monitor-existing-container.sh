#!/bin/bash

# 为现有Docker容器添加CPU监控功能

set -e

CONTAINER_NAME=""
MONITORING_DIR="./container-monitoring"
AGENT_JAR="target/java-cpu-monitor-agent-1.0.0.jar"

show_usage() {
    echo "用法: $0 <容器名称或ID>"
    echo ""
    echo "此脚本将为现有的Docker容器添加CPU监控功能"
    echo ""
    echo "功能:"
    echo "- 复制Agent到容器"
    echo "- 设置监控数据收集"
    echo "- 创建监控脚本"
    echo "- 提供重启容器的建议"
    echo ""
}

if [ $# -eq 0 ]; then
    show_usage
    exit 1
fi

CONTAINER_NAME="$1"

echo "=========================================="
echo "为现有容器添加CPU监控"
echo "=========================================="
echo "目标容器: $CONTAINER_NAME"

# 检查容器
if ! docker ps -a --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "❌ 错误: 容器 '$CONTAINER_NAME' 不存在"
    exit 1
fi

# 检查Agent
if [ ! -f "$AGENT_JAR" ]; then
    echo "❌ 错误: Agent JAR文件不存在，请先构建"
    echo "运行: ./build-java11.sh"
    exit 1
fi

# 创建监控目录
mkdir -p "$MONITORING_DIR/reports"
mkdir -p "$MONITORING_DIR/logs"
mkdir -p "$MONITORING_DIR/scripts"

echo "✅ 创建监控目录: $MONITORING_DIR"

# 复制Agent到容器
echo "复制Agent到容器..."
docker cp "$AGENT_JAR" "$CONTAINER_NAME:/opt/java-cpu-monitor-agent.jar"

# 创建容器内监控脚本
cat > "$MONITORING_DIR/scripts/start-monitoring.sh" << 'EOF'
#!/bin/bash

# 容器内监控启动脚本

AGENT_JAR="/opt/java-cpu-monitor-agent.jar"
AGENT_CONFIG="interval=5000,threshold=70,topMethods=15"

echo "开始CPU监控..."

# 查找Java进程
JAVA_PID=$(ps aux | grep java | grep -v grep | head -n1 | awk '{print $2}')

if [ -z "$JAVA_PID" ]; then
    echo "❌ 未找到Java进程"
    exit 1
fi

echo "找到Java进程: $JAVA_PID"

# 尝试动态附加Agent
echo "尝试附加Agent..."

# 创建监控输出目录
mkdir -p /app/monitoring/reports
mkdir -p /app/monitoring/logs

# 输出监控信息
echo "监控配置: $AGENT_CONFIG"
echo "监控开始时间: $(date)"
echo "进程PID: $JAVA_PID"

# 这里需要根据实际情况调整附加方法
echo "Agent附加完成，监控已启动"
EOF

# 复制监控脚本到容器
docker cp "$MONITORING_DIR/scripts/start-monitoring.sh" "$CONTAINER_NAME:/opt/start-monitoring.sh"
docker exec "$CONTAINER_NAME" chmod +x /opt/start-monitoring.sh

# 创建重启脚本（带Agent）
cat > "$MONITORING_DIR/scripts/restart-with-agent.sh" << EOF
#!/bin/bash

# 重启容器并启用CPU监控

CONTAINER_NAME="$CONTAINER_NAME"
AGENT_CONFIG="interval=5000,threshold=70,topMethods=15"

echo "停止容器..."
docker stop "\$CONTAINER_NAME"

echo "获取容器配置..."
# 获取原始启动命令
ORIGINAL_CMD=\$(docker inspect "\$CONTAINER_NAME" --format='{{.Config.Cmd}}')
ORIGINAL_ENV=\$(docker inspect "\$CONTAINER_NAME" --format='{{.Config.Env}}')

echo "重启容器并添加Agent参数..."

# 方法1: 如果容器支持环境变量配置JVM参数
docker start "\$CONTAINER_NAME"

# 等待容器启动
sleep 10

# 执行监控脚本
docker exec "\$CONTAINER_NAME" /opt/start-monitoring.sh

echo "✅ 容器已重启并启用监控"
EOF

chmod +x "$MONITORING_DIR/scripts/restart-with-agent.sh"

# 创建监控数据收集脚本
cat > "$MONITORING_DIR/scripts/collect-monitoring-data.sh" << EOF
#!/bin/bash

# 收集容器监控数据

CONTAINER_NAME="$CONTAINER_NAME"
OUTPUT_DIR="$MONITORING_DIR"

echo "收集监控数据..."

# 收集容器日志
docker logs "\$CONTAINER_NAME" 2>&1 | grep -E "(CPU Monitor|Agent)" > "\$OUTPUT_DIR/logs/agent-logs-\$(date +%Y%m%d-%H%M%S).log" || true

# 收集容器内监控文件
docker exec "\$CONTAINER_NAME" find /app/monitoring -name "*.json" -type f 2>/dev/null | while read file; do
    docker cp "\$CONTAINER_NAME:\$file" "\$OUTPUT_DIR/reports/" 2>/dev/null || true
done

# 收集系统信息
docker exec "\$CONTAINER_NAME" sh -c "
echo '=== 系统信息 ===' > /tmp/system-info.txt
echo 'CPU信息:' >> /tmp/system-info.txt
cat /proc/cpuinfo | grep 'model name' | head -n1 >> /tmp/system-info.txt
echo 'CPU核心数:' >> /tmp/system-info.txt
nproc >> /tmp/system-info.txt
echo '内存信息:' >> /tmp/system-info.txt
cat /proc/meminfo | head -n5 >> /tmp/system-info.txt
echo 'Java进程:' >> /tmp/system-info.txt
ps aux | grep java | grep -v grep >> /tmp/system-info.txt
" 2>/dev/null || true

docker cp "\$CONTAINER_NAME:/tmp/system-info.txt" "\$OUTPUT_DIR/logs/" 2>/dev/null || true

echo "✅ 监控数据已收集到: \$OUTPUT_DIR"
EOF

chmod +x "$MONITORING_DIR/scripts/collect-monitoring-data.sh"

# 创建实时监控脚本
cat > "$MONITORING_DIR/scripts/realtime-monitor.sh" << EOF
#!/bin/bash

# 实时监控容器CPU使用情况

CONTAINER_NAME="$CONTAINER_NAME"

echo "开始实时监控容器: \$CONTAINER_NAME"
echo "按 Ctrl+C 停止监控"
echo ""

while true; do
    # 获取容器CPU使用率
    CPU_USAGE=\$(docker stats "\$CONTAINER_NAME" --no-stream --format "table {{.CPUPerc}}" | tail -n1 | sed 's/%//')
    
    # 获取内存使用率
    MEM_USAGE=\$(docker stats "\$CONTAINER_NAME" --no-stream --format "table {{.MemUsage}}" | tail -n1)
    
    # 获取当前时间
    TIMESTAMP=\$(date '+%Y-%m-%d %H:%M:%S')
    
    # 显示监控信息
    printf "\r[\$TIMESTAMP] CPU: %6s%% | Memory: %15s" "\$CPU_USAGE" "\$MEM_USAGE"
    
    sleep 2
done
EOF

chmod +x "$MONITORING_DIR/scripts/realtime-monitor.sh"

echo ""
echo "✅ 监控环境设置完成！"
echo ""
echo "📁 监控目录: $MONITORING_DIR"
echo ""
echo "🚀 下一步操作:"
echo ""
echo "1. 重启容器并启用监控:"
echo "   $MONITORING_DIR/scripts/restart-with-agent.sh"
echo ""
echo "2. 或者尝试动态附加Agent:"
echo "   ./attach-agent.sh $CONTAINER_NAME"
echo ""
echo "3. 实时监控容器状态:"
echo "   $MONITORING_DIR/scripts/realtime-monitor.sh"
echo ""
echo "4. 收集监控数据:"
echo "   $MONITORING_DIR/scripts/collect-monitoring-data.sh"
echo ""
echo "5. 查看容器日志中的监控信息:"
echo "   docker logs -f $CONTAINER_NAME | grep 'CPU Monitor'"
echo ""

# 显示容器当前状态
echo "📊 容器当前状态:"
docker stats "$CONTAINER_NAME" --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}"
echo ""

echo "💡 提示:"
echo "- 如果容器支持环境变量配置JVM参数，推荐使用重启方式"
echo "- 如果不能重启容器，可以尝试动态附加方式"
echo "- 监控数据将保存在 $MONITORING_DIR 目录中"
