#!/bin/bash

# Java CPU Monitor Agent 安装脚本

set -e

echo "安装 Java CPU Monitor Agent..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境"
    echo "请安装Java 8或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
echo "✅ Java版本: $JAVA_VERSION"

# 创建安装目录
INSTALL_DIR="/opt/java-cpu-monitor-agent"
sudo mkdir -p "$INSTALL_DIR"

# 复制文件
sudo cp java-cpu-monitor-agent.jar "$INSTALL_DIR/"
sudo cp *.sh "$INSTALL_DIR/"
sudo chmod +x "$INSTALL_DIR"/*.sh

# 创建符号链接
sudo ln -sf "$INSTALL_DIR/java-cpu-monitor-agent.jar" /usr/local/bin/java-cpu-monitor-agent.jar

echo "✅ 安装完成!"
echo ""
echo "使用方法:"
echo "java -javaagent:/usr/local/bin/java-cpu-monitor-agent.jar=interval=5000,threshold=70 -jar your-app.jar"
