#!/bin/bash

# Java CPU Monitor Agent 构建脚本 (Java 11优化版本)

set -e

echo "=========================================="
echo "Java CPU Monitor Agent Build Script"
echo "Java 11 Optimized Version"
echo "=========================================="

# 检查Java版本
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 11或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
echo "Java版本: $JAVA_VERSION"

# 检查Java版本是否为11或更高
if [ "$JAVA_VERSION" -lt 11 ]; then
    echo "警告: 检测到Java版本低于11，建议使用Java 11或更高版本以获得最佳性能"
    echo "当前版本: $JAVA_VERSION"
    echo "是否继续构建? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "构建已取消"
        exit 1
    fi
else
    echo "✅ Java版本符合要求 (Java $JAVA_VERSION >= 11)"
fi

# 检查Maven
echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请安装Maven 3.6或更高版本"
    exit 1
fi

MVN_VERSION=$(mvn -version | head -n1 | cut -d' ' -f3)
echo "Maven版本: $MVN_VERSION"

# 设置Java 11特定的JVM参数
export MAVEN_OPTS="-Xmx2g -XX:+UseG1GC -XX:+UseStringDeduplication"

# 清理之前的构建
echo "清理之前的构建..."
mvn clean

# 编译项目 (Java 11优化)
echo "编译项目 (Java 11优化)..."
mvn compile -Dmaven.compiler.release=11

# 运行测试
echo "运行测试..."
mvn test -q

# 打包项目
echo "打包项目..."
mvn package -q

# 检查生成的JAR文件
AGENT_JAR="target/java-cpu-monitor-agent-1.0.0.jar"
if [ -f "$AGENT_JAR" ]; then
    echo "✅ Agent JAR文件构建成功: $AGENT_JAR"
    echo "文件大小: $(du -h $AGENT_JAR | cut -f1)"
    
    # 验证JAR文件的Java版本
    echo "验证JAR文件兼容性..."
    if command -v javap &> /dev/null; then
        CLASS_VERSION=$(javap -verbose -cp "$AGENT_JAR" com.monitor.agent.CpuMonitorAgent | grep "major version" | head -n1 | cut -d':' -f2 | tr -d ' ')
        if [ "$CLASS_VERSION" = "55" ]; then
            echo "✅ JAR文件已编译为Java 11字节码"
        else
            echo "ℹ️ JAR文件字节码版本: $CLASS_VERSION"
        fi
    fi
else
    echo "❌ Agent JAR文件构建失败"
    exit 1
fi

# 检查测试类
TEST_CLASSES="target/test-classes/com/monitor/agent/test/TestApplication.class"
if [ -f "$TEST_CLASSES" ]; then
    echo "✅ 测试类编译成功"
else
    echo "❌ 测试类编译失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "Java 11优化构建完成！"
echo "=========================================="
echo ""
echo "Java 11特性优化："
echo "- 使用String.repeat()方法优化字符串操作"
echo "- 启用G1垃圾收集器和字符串去重"
echo "- 使用--release编译选项确保兼容性"
echo ""
echo "使用方法："
echo "1. 启动时加载Agent (Java 11+):"
echo "   java --add-opens java.management/sun.management=ALL-UNNAMED \\"
echo "        -javaagent:$AGENT_JAR=interval=5000,threshold=70 \\"
echo "        -cp target/test-classes \\"
echo "        com.monitor.agent.test.TestApplication"
echo ""
echo "2. 高性能模式 (Java 11+):"
echo "   java -XX:+UseG1GC -XX:+UseStringDeduplication \\"
echo "        -javaagent:$AGENT_JAR=interval=1000,threshold=80 \\"
echo "        -jar your-application.jar"
echo ""
echo "Java 11专用参数说明："
echo "- --add-opens: 允许访问JDK内部API"
echo "- -XX:+UseG1GC: 使用G1垃圾收集器"
echo "- -XX:+UseStringDeduplication: 启用字符串去重"
echo ""
