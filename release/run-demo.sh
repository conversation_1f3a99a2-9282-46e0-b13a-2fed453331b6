#!/bin/bash

# Java CPU Monitor Agent 演示脚本

set -e

echo "=========================================="
echo "Java CPU Monitor Agent Demo"
echo "=========================================="

# 检查Agent JAR是否存在
AGENT_JAR="target/java-cpu-monitor-agent-1.0.0.jar"
if [ ! -f "$AGENT_JAR" ]; then
    echo "Agent JAR文件不存在，正在构建..."
    ./build.sh
fi

# 检查测试应用是否存在
TEST_CLASSES="target/test-classes/com/monitor/agent/test/TestApplication.class"
if [ ! -f "$TEST_CLASSES" ]; then
    echo "测试应用不存在，请先运行 ./build.sh"
    exit 1
fi

echo ""
echo "启动CPU监控演示..."
echo "Agent配置: 采样间隔=3秒, CPU阈值=50%, 显示前15个热点方法"
echo ""
echo "提示: 按 Ctrl+C 停止演示"
echo ""

# 启动演示应用
java -javaagent:$AGENT_JAR=interval=3000,threshold=50,topMethods=15 \
     -cp target/test-classes \
     com.monitor.agent.test.TestApplication
