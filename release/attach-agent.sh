#!/bin/bash

# 动态附加CPU Monitor Agent到运行中的Docker容器

set -e

# 配置参数
CONTAINER_NAME=""
AGENT_JAR="target/java-cpu-monitor-agent-1.0.0.jar"
AGENT_CONFIG="interval=5000,threshold=70,topMethods=15"

# 显示使用方法
show_usage() {
    echo "用法: $0 [选项] <容器名称或ID>"
    echo ""
    echo "选项:"
    echo "  -c, --config CONFIG    Agent配置参数 (默认: interval=5000,threshold=70,topMethods=15)"
    echo "  -j, --jar JAR_PATH     Agent JAR文件路径 (默认: target/java-cpu-monitor-agent-1.0.0.jar)"
    echo "  -h, --help             显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 my-java-app"
    echo "  $0 -c 'interval=3000,threshold=80' my-java-app"
    echo "  $0 --jar /path/to/agent.jar my-java-app"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            AGENT_CONFIG="$2"
            shift 2
            ;;
        -j|--jar)
            AGENT_JAR="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        -*)
            echo "未知选项: $1"
            show_usage
            exit 1
            ;;
        *)
            CONTAINER_NAME="$1"
            shift
            ;;
    esac
done

# 检查必需参数
if [ -z "$CONTAINER_NAME" ]; then
    echo "❌ 错误: 请指定容器名称或ID"
    show_usage
    exit 1
fi

# 检查Agent JAR文件
if [ ! -f "$AGENT_JAR" ]; then
    echo "❌ 错误: Agent JAR文件不存在: $AGENT_JAR"
    echo "请先构建Agent: ./build-java11.sh"
    exit 1
fi

echo "=========================================="
echo "动态附加CPU Monitor Agent"
echo "=========================================="
echo "目标容器: $CONTAINER_NAME"
echo "Agent JAR: $AGENT_JAR"
echo "Agent配置: $AGENT_CONFIG"
echo ""

# 检查容器是否存在和运行
if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
    echo "❌ 错误: 容器 '$CONTAINER_NAME' 不存在或未运行"
    echo ""
    echo "当前运行的容器:"
    docker ps --format "table {{.Names}}\t{{.Image}}\t{{.Status}}"
    exit 1
fi

echo "✅ 容器 '$CONTAINER_NAME' 正在运行"

# 获取容器中的Java进程
echo "查找Java进程..."
JAVA_PIDS=$(docker exec "$CONTAINER_NAME" sh -c "ps aux | grep java | grep -v grep | awk '{print \$2}'" 2>/dev/null || echo "")

if [ -z "$JAVA_PIDS" ]; then
    echo "❌ 错误: 在容器中未找到Java进程"
    echo ""
    echo "容器中的进程列表:"
    docker exec "$CONTAINER_NAME" ps aux
    exit 1
fi

echo "找到Java进程: $JAVA_PIDS"

# 选择主Java进程（通常是PID最小的）
MAIN_PID=$(echo "$JAVA_PIDS" | head -n1)
echo "选择主进程PID: $MAIN_PID"

# 复制Agent JAR到容器
CONTAINER_AGENT_PATH="/tmp/java-cpu-monitor-agent.jar"
echo "复制Agent到容器..."
docker cp "$AGENT_JAR" "$CONTAINER_NAME:$CONTAINER_AGENT_PATH"

if [ $? -ne 0 ]; then
    echo "❌ 错误: 无法复制Agent到容器"
    exit 1
fi

echo "✅ Agent已复制到容器: $CONTAINER_AGENT_PATH"

# 检查容器中是否有Java attach工具
echo "检查Java attach工具..."
HAS_JCMD=$(docker exec "$CONTAINER_NAME" sh -c "command -v jcmd" 2>/dev/null || echo "")
HAS_JAVA=$(docker exec "$CONTAINER_NAME" sh -c "command -v java" 2>/dev/null || echo "")

if [ -z "$HAS_JAVA" ]; then
    echo "❌ 错误: 容器中未找到java命令"
    exit 1
fi

# 创建attach脚本
ATTACH_SCRIPT="/tmp/attach-agent.sh"
cat > "$ATTACH_SCRIPT" << EOF
#!/bin/bash
set -e

echo "开始附加Agent到进程 $MAIN_PID..."

# 方法1: 使用Java attach API
java -cp "$CONTAINER_AGENT_PATH" com.sun.tools.attach.VirtualMachine.attach $MAIN_PID "$CONTAINER_AGENT_PATH" "$AGENT_CONFIG" 2>/dev/null || {
    echo "方法1失败，尝试方法2..."
    
    # 方法2: 使用jcmd (如果可用)
    if command -v jcmd >/dev/null 2>&1; then
        jcmd $MAIN_PID JVMTI.agent_load "$CONTAINER_AGENT_PATH" "$AGENT_CONFIG" || {
            echo "方法2失败，尝试方法3..."
            
            # 方法3: 使用kill -3发送信号让应用加载Agent
            echo "发送信号给进程..."
            kill -USR2 $MAIN_PID 2>/dev/null || echo "信号发送失败"
        }
    else
        echo "jcmd不可用，跳过方法2"
    fi
}

echo "Agent附加完成"
EOF

# 复制attach脚本到容器并执行
docker cp "$ATTACH_SCRIPT" "$CONTAINER_NAME:/tmp/attach-agent.sh"
docker exec "$CONTAINER_NAME" chmod +x /tmp/attach-agent.sh

echo ""
echo "执行Agent附加..."
docker exec "$CONTAINER_NAME" /tmp/attach-agent.sh

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Agent附加成功！"
    echo ""
    echo "监控信息:"
    echo "- 采样间隔: $(echo $AGENT_CONFIG | grep -o 'interval=[0-9]*' | cut -d= -f2)ms"
    echo "- CPU阈值: $(echo $AGENT_CONFIG | grep -o 'threshold=[0-9]*' | cut -d= -f2)%"
    echo "- 热点方法数: $(echo $AGENT_CONFIG | grep -o 'topMethods=[0-9]*' | cut -d= -f2)"
    echo ""
    echo "查看监控日志:"
    echo "docker logs -f $CONTAINER_NAME | grep 'CPU Monitor'"
    echo ""
    echo "进入容器查看详细信息:"
    echo "docker exec -it $CONTAINER_NAME bash"
else
    echo "❌ Agent附加失败"
    echo ""
    echo "故障排除建议:"
    echo "1. 确保容器中的Java进程支持动态attach"
    echo "2. 检查容器的安全策略和权限"
    echo "3. 尝试重启容器并在启动时加载Agent"
    exit 1
fi

# 清理临时文件
rm -f "$ATTACH_SCRIPT"
docker exec "$CONTAINER_NAME" rm -f /tmp/attach-agent.sh 2>/dev/null || true

echo "清理完成"
