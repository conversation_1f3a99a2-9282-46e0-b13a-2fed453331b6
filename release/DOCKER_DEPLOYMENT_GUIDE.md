# Docker环境部署指南

## 🐳 Docker容器中使用Java CPU Monitor Agent

### 部署方案概览

| 方案 | 适用场景 | 优点 | 缺点 |
|------|----------|------|------|
| **构建时集成** | 新项目或可重新构建 | 性能最佳，配置简单 | 需要重新构建镜像 |
| **动态附加** | 现有运行容器 | 无需重启，即时生效 | 可能有兼容性问题 |
| **现有容器改造** | 生产环境 | 风险最小，渐进式 | 需要重启容器 |

## 🚀 方案一：构建时集成Agent（推荐）

### 1. 准备工作

```bash
# 构建Agent
./build-java11.sh

# 确保有以下文件
ls -la Dockerfile.with-agent docker-entrypoint.sh docker-compose.yml
```

### 2. 修改您的应用Dockerfile

将您现有的应用JAR复制到Dockerfile中：

```dockerfile
# 在Dockerfile.with-agent中添加这一行（替换注释）
COPY your-application.jar /app/app.jar
```

### 3. 构建和运行

```bash
# 构建包含Agent的镜像
docker build -f Dockerfile.with-agent -t your-app:with-monitor .

# 使用docker-compose运行
docker-compose up -d

# 或直接运行
docker run -d \
  --name java-app-monitor \
  -p 8080:8080 \
  -v $(pwd)/monitoring-data:/app/monitoring \
  -e AGENT_OPTS="interval=3000,threshold=60,topMethods=20" \
  your-app:with-monitor
```

### 4. 监控数据查看

```bash
# 查看实时日志
docker logs -f java-app-monitor | grep "CPU Monitor"

# 查看监控报告
ls -la monitoring-data/reports/

# 查看JSON报告
cat monitoring-data/reports/cpu-report-*.json | jq .
```

## ⚡ 方案二：动态附加Agent

### 1. 对现有运行容器附加Agent

```bash
# 构建Agent
./build-java11.sh

# 附加到现有容器
./attach-agent.sh your-existing-container

# 自定义配置附加
./attach-agent.sh -c "interval=2000,threshold=80,topMethods=25" your-container
```

### 2. 验证附加结果

```bash
# 查看容器日志
docker logs -f your-existing-container | grep "CPU Monitor"

# 检查Agent是否加载成功
docker exec your-existing-container ps aux | grep java
```

## 🔧 方案三：现有容器改造

### 1. 为现有容器添加监控

```bash
# 设置监控环境
./monitor-existing-container.sh your-existing-container

# 这将创建 ./container-monitoring/ 目录和相关脚本
```

### 2. 重启容器启用监控

```bash
# 使用生成的重启脚本
./container-monitoring/scripts/restart-with-agent.sh

# 或手动重启并添加Agent参数
docker stop your-container
docker start your-container
```

### 3. 监控数据收集

```bash
# 实时监控
./container-monitoring/scripts/realtime-monitor.sh

# 收集监控数据
./container-monitoring/scripts/collect-monitoring-data.sh
```

## 📊 监控配置优化

### Docker环境专用配置

```bash
# 高频监控（开发/测试环境）
AGENT_OPTS="interval=1000,threshold=50,topMethods=30"

# 标准监控（生产环境）
AGENT_OPTS="interval=5000,threshold=70,topMethods=15"

# 低频监控（资源受限环境）
AGENT_OPTS="interval=10000,threshold=80,topMethods=10"
```

### JVM参数优化

```bash
# Docker容器推荐JVM参数
JAVA_OPTS="-XX:+UseG1GC \
           -XX:+UseStringDeduplication \
           -XX:+UnlockExperimentalVMOptions \
           -XX:+UseCGroupMemoryLimitForHeap \
           -XX:MaxGCPauseMillis=200"
```

## 🔍 故障排除

### 常见问题

**Q: Agent无法附加到容器中的Java进程**
```bash
# 检查Java进程
docker exec your-container ps aux | grep java

# 检查JVM参数是否支持attach
docker exec your-container jcmd <PID> VM.flags | grep DisableAttachMechanism
```

**Q: 容器中看不到监控输出**
```bash
# 检查Agent是否正确加载
docker logs your-container | grep -i agent

# 检查监控目录权限
docker exec your-container ls -la /app/monitoring/
```

**Q: 监控数据丢失**
```bash
# 确保挂载了数据卷
docker inspect your-container | grep -A 10 "Mounts"

# 检查数据目录
ls -la ./monitoring-data/
```

### 性能调优

**内存限制环境：**
```bash
# 减少采样频率
AGENT_OPTS="interval=10000,threshold=80,topMethods=5"

# 限制JVM堆内存
JVM_MEMORY="-Xms256m -Xmx1g"
```

**高性能环境：**
```bash
# 增加采样频率
AGENT_OPTS="interval=1000,threshold=60,topMethods=50"

# 使用G1GC
JAVA_OPTS="-XX:+UseG1GC -XX:MaxGCPauseMillis=100"
```

## 📈 监控数据分析

### 查看实时CPU使用率

```bash
# 容器级别监控
docker stats your-container --no-stream

# 应用级别监控（通过Agent）
docker logs your-container | grep "currentUsage" | tail -5
```

### 导出监控报告

```bash
# 导出最新的JSON报告
docker exec your-container find /app/monitoring -name "*.json" -type f -exec cat {} \; | jq .

# 生成CSV格式报告
docker exec your-container sh -c "
  grep 'currentUsage' /app/monitoring/logs/*.log | 
  sed 's/.*currentUsage.*: \"\([0-9.]*\)%\".*/\1/' > /tmp/cpu-usage.csv
"
docker cp your-container:/tmp/cpu-usage.csv ./cpu-usage.csv
```

## 🔐 安全考虑

### 容器权限

```yaml
# docker-compose.yml 安全配置
services:
  java-app:
    security_opt:
      - no-new-privileges:true
    read_only: true
    tmpfs:
      - /tmp
      - /app/monitoring
```

### 网络隔离

```yaml
# 限制网络访问
networks:
  monitoring:
    driver: bridge
    internal: true  # 内部网络，无外网访问
```

## 📝 最佳实践

1. **生产环境建议**：使用构建时集成方案
2. **开发环境建议**：使用动态附加方案快速调试
3. **监控数据持久化**：始终挂载数据卷
4. **资源限制**：设置合理的CPU和内存限制
5. **日志管理**：定期清理监控日志文件
6. **安全性**：限制容器权限和网络访问

## 🎯 快速开始

```bash
# 1. 构建Agent
./build-java11.sh

# 2. 选择部署方案
# 新项目：使用 Dockerfile.with-agent
# 现有容器：使用 ./attach-agent.sh your-container
# 生产环境：使用 ./monitor-existing-container.sh your-container

# 3. 查看监控结果
docker logs -f your-container | grep "CPU Monitor"
```
