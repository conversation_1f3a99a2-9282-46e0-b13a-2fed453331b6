# Java CPU Monitor Agent - 快速开始

## 🚀 5分钟快速上手

### 1. 基本使用
```bash
# 监控现有Java应用
java -javaagent:java-cpu-monitor-agent.jar=interval=5000,threshold=70 \
     -jar your-application.jar

# 查看监控报告
# Agent会自动输出JSON格式的性能报告
```

### 2. Docker环境
```bash
# 动态附加到运行中的容器
./attach-agent.sh your-container-name

# 或重新构建包含Agent的镜像
docker build -f Dockerfile.with-agent -t your-app:monitored .
```

### 3. 配置参数
- `interval=5000` - 采样间隔(毫秒)
- `threshold=70` - CPU阈值(百分比)  
- `topMethods=10` - 显示热点方法数量

### 4. 查看完整文档
- [README.md](README.md) - 完整使用指南
- [DOCKER_DEPLOYMENT_GUIDE.md](DOCKER_DEPLOYMENT_GUIDE.md) - Docker部署指南

## 💡 常见用法

**开发环境 - 高频监控:**
```bash
java -javaagent:java-cpu-monitor-agent.jar=interval=2000,threshold=50,topMethods=20 \
     -jar your-app.jar
```

**生产环境 - 标准监控:**
```bash
java -javaagent:java-cpu-monitor-agent.jar=interval=10000,threshold=80,topMethods=10 \
     -jar your-app.jar
```

**Docker容器 - 动态附加:**
```bash
./attach-agent.sh -c "interval=5000,threshold=70" your-container
```

## 🔍 故障排除

1. **Agent无法加载**: 检查Java版本(需要8+)和JAR文件路径
2. **无监控输出**: 降低threshold参数值
3. **性能影响**: 增加interval参数值

更多帮助请查看完整文档或提交Issue。
