# 多阶段构建Dockerfile - 集成CPU Monitor Agent
FROM maven:3.8.6-openjdk-11-slim AS agent-builder

# 构建Agent
WORKDIR /agent-build
COPY pom.xml .
COPY src ./src
COPY build-java11.sh .

# 构建Agent JAR
RUN chmod +x build-java11.sh && \
    ./build-java11.sh && \
    cp target/java-cpu-monitor-agent-1.0.0.jar /agent.jar

# 应用镜像
FROM openjdk:11-jre-slim

# 安装必要工具
RUN apt-get update && \
    apt-get install -y curl procps && \
    rm -rf /var/lib/apt/lists/*

# 创建应用目录
WORKDIR /app

# 从构建阶段复制Agent
COPY --from=agent-builder /agent.jar /opt/java-cpu-monitor-agent.jar

# 复制您的应用JAR（需要替换为实际的应用JAR）
# COPY your-application.jar /app/app.jar

# 设置JVM参数和Agent配置
ENV JAVA_OPTS="-XX:+UseG1GC -XX:+UseStringDeduplication -XX:MaxGCPauseMillis=200"
ENV AGENT_OPTS="interval=5000,threshold=70,topMethods=15"
ENV JVM_MEMORY="-Xms512m -Xmx2g"

# 创建监控数据目录
RUN mkdir -p /app/monitoring/reports && \
    mkdir -p /app/monitoring/logs

# 设置监控数据卷
VOLUME ["/app/monitoring"]

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动脚本
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh

# 暴露端口（根据您的应用调整）
EXPOSE 8080

# 启动应用
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["java", "-jar", "/app/app.jar"]
