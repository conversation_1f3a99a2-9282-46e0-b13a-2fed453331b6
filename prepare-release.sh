#!/bin/bash

# 发布准备脚本

set -e

echo "=========================================="
echo "Java CPU Monitor Agent - 发布准备"
echo "=========================================="

# 检查Git状态
if [ ! -d ".git" ]; then
    echo "❌ 错误: Git仓库未初始化"
    echo "请先运行: ./git-setup.sh"
    exit 1
fi

# 检查是否有未提交的更改
if ! git diff --quiet || ! git diff --cached --quiet; then
    echo "⚠️ 警告: 有未提交的更改"
    echo "未暂存的文件:"
    git diff --name-only
    echo "已暂存的文件:"
    git diff --cached --name-only
    echo ""
    echo "是否继续? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "已取消"
        exit 1
    fi
fi

# 检查构建状态
echo "🔨 检查构建状态..."
if [ ! -f "target/java-cpu-monitor-agent-1.0.0.jar" ]; then
    echo "⚠️ Agent JAR文件不存在，正在构建..."
    ./build-java11.sh
fi

echo "✅ Agent JAR文件: $(du -h target/java-cpu-monitor-agent-1.0.0.jar | cut -f1)"

# 创建发布目录
RELEASE_DIR="release"
mkdir -p "$RELEASE_DIR"

echo "📦 准备发布文件..."

# 复制主要文件
cp target/java-cpu-monitor-agent-1.0.0.jar "$RELEASE_DIR/java-cpu-monitor-agent.jar"
cp README.md "$RELEASE_DIR/"
cp LICENSE "$RELEASE_DIR/"
cp CHANGELOG.md "$RELEASE_DIR/"
cp DOCKER_DEPLOYMENT_GUIDE.md "$RELEASE_DIR/"

# 复制脚本
cp build.sh "$RELEASE_DIR/"
cp build-java11.sh "$RELEASE_DIR/"
cp run-demo.sh "$RELEASE_DIR/"
cp attach-agent.sh "$RELEASE_DIR/"
cp monitor-existing-container.sh "$RELEASE_DIR/"

# 复制Docker文件
cp docker-compose.yml "$RELEASE_DIR/"
cp Dockerfile.with-agent "$RELEASE_DIR/"
cp docker-entrypoint.sh "$RELEASE_DIR/"

# 创建快速开始指南
cat > "$RELEASE_DIR/QUICK_START.md" << 'EOF'
# Java CPU Monitor Agent - 快速开始

## 🚀 5分钟快速上手

### 1. 基本使用
```bash
# 监控现有Java应用
java -javaagent:java-cpu-monitor-agent.jar=interval=5000,threshold=70 \
     -jar your-application.jar

# 查看监控报告
# Agent会自动输出JSON格式的性能报告
```

### 2. Docker环境
```bash
# 动态附加到运行中的容器
./attach-agent.sh your-container-name

# 或重新构建包含Agent的镜像
docker build -f Dockerfile.with-agent -t your-app:monitored .
```

### 3. 配置参数
- `interval=5000` - 采样间隔(毫秒)
- `threshold=70` - CPU阈值(百分比)  
- `topMethods=10` - 显示热点方法数量

### 4. 查看完整文档
- [README.md](README.md) - 完整使用指南
- [DOCKER_DEPLOYMENT_GUIDE.md](DOCKER_DEPLOYMENT_GUIDE.md) - Docker部署指南

## 💡 常见用法

**开发环境 - 高频监控:**
```bash
java -javaagent:java-cpu-monitor-agent.jar=interval=2000,threshold=50,topMethods=20 \
     -jar your-app.jar
```

**生产环境 - 标准监控:**
```bash
java -javaagent:java-cpu-monitor-agent.jar=interval=10000,threshold=80,topMethods=10 \
     -jar your-app.jar
```

**Docker容器 - 动态附加:**
```bash
./attach-agent.sh -c "interval=5000,threshold=70" your-container
```

## 🔍 故障排除

1. **Agent无法加载**: 检查Java版本(需要8+)和JAR文件路径
2. **无监控输出**: 降低threshold参数值
3. **性能影响**: 增加interval参数值

更多帮助请查看完整文档或提交Issue。
EOF

# 创建安装脚本
cat > "$RELEASE_DIR/install.sh" << 'EOF'
#!/bin/bash

# Java CPU Monitor Agent 安装脚本

set -e

echo "安装 Java CPU Monitor Agent..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境"
    echo "请安装Java 8或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
echo "✅ Java版本: $JAVA_VERSION"

# 创建安装目录
INSTALL_DIR="/opt/java-cpu-monitor-agent"
sudo mkdir -p "$INSTALL_DIR"

# 复制文件
sudo cp java-cpu-monitor-agent.jar "$INSTALL_DIR/"
sudo cp *.sh "$INSTALL_DIR/"
sudo chmod +x "$INSTALL_DIR"/*.sh

# 创建符号链接
sudo ln -sf "$INSTALL_DIR/java-cpu-monitor-agent.jar" /usr/local/bin/java-cpu-monitor-agent.jar

echo "✅ 安装完成!"
echo ""
echo "使用方法:"
echo "java -javaagent:/usr/local/bin/java-cpu-monitor-agent.jar=interval=5000,threshold=70 -jar your-app.jar"
EOF

chmod +x "$RELEASE_DIR/install.sh"

# 创建发布包
echo "📦 创建发布包..."

VERSION="v1.0.0"
ARCHIVE_NAME="java-cpu-monitor-agent-$VERSION"

# 创建tar.gz包
tar -czf "$ARCHIVE_NAME.tar.gz" -C "$RELEASE_DIR" .

# 创建zip包
(cd "$RELEASE_DIR" && zip -r "../$ARCHIVE_NAME.zip" .)

echo "✅ 发布包已创建:"
echo "   - $ARCHIVE_NAME.tar.gz ($(du -h $ARCHIVE_NAME.tar.gz | cut -f1))"
echo "   - $ARCHIVE_NAME.zip ($(du -h $ARCHIVE_NAME.zip | cut -f1))"

# 生成校验和
echo "🔐 生成校验和..."
if command -v sha256sum >/dev/null 2>&1; then
    sha256sum "$ARCHIVE_NAME.tar.gz" > "$ARCHIVE_NAME.tar.gz.sha256"
    sha256sum "$ARCHIVE_NAME.zip" > "$ARCHIVE_NAME.zip.sha256"
elif command -v shasum >/dev/null 2>&1; then
    shasum -a 256 "$ARCHIVE_NAME.tar.gz" > "$ARCHIVE_NAME.tar.gz.sha256"
    shasum -a 256 "$ARCHIVE_NAME.zip" > "$ARCHIVE_NAME.zip.sha256"
else
    echo "⚠️ 警告: 未找到sha256sum或shasum命令，跳过校验和生成"
fi

echo "✅ 校验和文件已生成"

# 显示发布信息
echo ""
echo "=========================================="
echo "发布信息"
echo "=========================================="
echo "版本: $VERSION"
echo "提交: $(git rev-parse --short HEAD)"
echo "日期: $(date '+%Y-%m-%d %H:%M:%S')"
echo ""
echo "发布文件:"
ls -la *.tar.gz *.zip *.sha256 2>/dev/null || true
echo ""
echo "发布目录内容:"
ls -la "$RELEASE_DIR/"

echo ""
echo "=========================================="
echo "GitHub发布步骤"
echo "=========================================="
echo ""
echo "1. 推送到GitHub:"
echo "   git remote add origin https://github.com/YOUR_USERNAME/java-cpu-monitor-agent.git"
echo "   git push -u origin main"
echo ""
echo "2. 创建发布标签:"
echo "   git tag -a $VERSION -m 'Release $VERSION'"
echo "   git push origin $VERSION"
echo ""
echo "3. 在GitHub上创建Release:"
echo "   - 访问: https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/releases/new"
echo "   - 选择标签: $VERSION"
echo "   - 标题: Java CPU Monitor Agent $VERSION"
echo "   - 描述: 复制CHANGELOG.md中的内容"
echo "   - 上传文件: $ARCHIVE_NAME.tar.gz, $ARCHIVE_NAME.zip 和对应的.sha256文件"
echo ""
echo "4. 发布完成后，用户可以通过以下方式下载:"
echo "   wget https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/releases/download/$VERSION/$ARCHIVE_NAME.tar.gz"
echo ""

echo "🎉 发布准备完成!"
