#!/bin/bash

# Git仓库设置和首次提交脚本

set -e

echo "=========================================="
echo "Java CPU Monitor Agent - Git设置"
echo "=========================================="

# 检查是否已经是Git仓库
if [ -d ".git" ]; then
    echo "✅ 已存在Git仓库"
else
    echo "初始化Git仓库..."
    git init
    echo "✅ Git仓库初始化完成"
fi

# 检查Git配置
echo "检查Git配置..."
if ! git config user.name >/dev/null 2>&1; then
    echo "请设置Git用户名:"
    read -p "用户名: " username
    git config user.name "$username"
fi

if ! git config user.email >/dev/null 2>&1; then
    echo "请设置Git邮箱:"
    read -p "邮箱: " email
    git config user.email "$email"
fi

echo "Git用户: $(git config user.name) <$(git config user.email)>"

# 添加所有文件
echo "添加文件到Git..."
git add .

# 检查状态
echo "Git状态:"
git status --short

# 创建首次提交
echo ""
echo "创建首次提交..."
git commit -m "feat: 初始提交 - Java CPU Monitor Agent v1.0.0

🎉 首次发布Java CPU Monitor Agent

核心功能:
- ✅ 实时CPU性能监控
- ✅ Java Agent技术实现
- ✅ 支持Java 8-21全版本
- ✅ Docker容器部署支持
- ✅ 动态Agent附加功能
- ✅ JSON和控制台报告格式
- ✅ 可配置监控参数

技术特性:
- 🛠 ByteBuddy字节码操作
- 🛠 ThreadMXBean CPU监控
- 🛠 Maven构建系统
- 🛠 多线程安全设计
- 🛠 G1GC性能优化

部署方案:
- 🐳 构建时集成Agent
- 🐳 运行时动态附加
- 🐳 现有容器改造
- 🐳 Docker Compose配置

文档完整:
- 📚 使用指南和API文档
- 📚 Docker部署指南
- 📚 贡献指南和开发文档
- 📚 CI/CD流水线配置"

echo "✅ 首次提交完成"

# 显示提交信息
echo ""
echo "提交信息:"
git log --oneline -1

echo ""
echo "=========================================="
echo "下一步: 推送到GitHub"
echo "=========================================="
echo ""
echo "1. 在GitHub上创建新仓库 'java-cpu-monitor-agent'"
echo ""
echo "2. 添加远程仓库并推送:"
echo "   git remote add origin https://github.com/YOUR_USERNAME/java-cpu-monitor-agent.git"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "3. 或者使用SSH:"
echo "   git remote <NAME_EMAIL>:YOUR_USERNAME/java-cpu-monitor-agent.git"
echo "   git branch -M main"
echo "   git push -u origin main"
echo ""
echo "4. 创建发布版本:"
echo "   git tag -a v1.0.0 -m 'Release v1.0.0'"
echo "   git push origin v1.0.0"
echo ""

# 提供快速设置选项
echo "是否现在设置GitHub远程仓库? (y/N)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    echo ""
    echo "请输入您的GitHub用户名:"
    read -p "GitHub用户名: " github_username
    
    echo ""
    echo "选择连接方式:"
    echo "1) HTTPS"
    echo "2) SSH"
    read -p "选择 (1/2): " connection_type
    
    if [ "$connection_type" = "1" ]; then
        REMOTE_URL="https://github.com/$github_username/java-cpu-monitor-agent.git"
    else
        REMOTE_URL="**************:$github_username/java-cpu-monitor-agent.git"
    fi
    
    echo ""
    echo "添加远程仓库: $REMOTE_URL"
    git remote add origin "$REMOTE_URL"
    
    echo "设置主分支..."
    git branch -M main
    
    echo ""
    echo "准备推送到GitHub..."
    echo "请确保您已在GitHub上创建了仓库: java-cpu-monitor-agent"
    echo ""
    echo "是否现在推送? (y/N)"
    read -r push_response
    if [[ "$push_response" =~ ^[Yy]$ ]]; then
        echo "推送到GitHub..."
        git push -u origin main
        
        echo ""
        echo "创建v1.0.0标签..."
        git tag -a v1.0.0 -m "Release v1.0.0 - Java CPU Monitor Agent

🎉 首次发布版本

主要功能:
- 实时CPU性能监控
- Java Agent技术实现  
- Docker容器部署支持
- 多Java版本兼容
- 完整的部署文档

下载地址:
- JAR文件: java-cpu-monitor-agent.jar
- 完整包: java-cpu-monitor-agent-v1.0.0.tar.gz"

        git push origin v1.0.0
        
        echo ""
        echo "🎉 成功推送到GitHub!"
        echo ""
        echo "仓库地址: https://github.com/$github_username/java-cpu-monitor-agent"
        echo "发布页面: https://github.com/$github_username/java-cpu-monitor-agent/releases"
        echo ""
    fi
fi

echo "Git设置完成!"
