# Java CPU Monitor Agent - Git忽略文件

# Maven构建目录
target/
dependency-reduced-pom.xml

# IDE文件
.idea/
*.iml
*.ipr
*.iws
.vscode/
.settings/
.project
.classpath

# 编译文件
*.class
*.jar
*.war
*.ear
*.nar

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 监控数据目录
monitoring-data/
container-monitoring/
app-logs/

# 测试输出
test-output/
*.hprof

# 环境配置
.env
.env.local
.env.production

# Docker相关
docker-compose.override.yml

# 备份文件
*.bak
*.backup

# 包管理器
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 其他
*.pid
*.seed
*.pid.lock
