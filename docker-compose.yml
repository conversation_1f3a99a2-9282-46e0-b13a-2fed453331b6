version: '3.8'

services:
  # 您的Java应用服务（集成CPU Monitor Agent）
  java-app-with-monitor:
    build:
      context: .
      dockerfile: Dockerfile.with-agent
    container_name: java-app-monitor
    ports:
      - "8080:8080"  # 应用端口
      - "9999:9999"  # 可选：JMX监控端口
    environment:
      # JVM内存配置
      JVM_MEMORY: "-Xms1g -Xmx4g"
      
      # JVM性能参数
      JAVA_OPTS: >-
        -XX:+UseG1GC
        -XX:+UseStringDeduplication
        -XX:MaxGCPauseMillis=200
        -XX:+UnlockExperimentalVMOptions
        -XX:+UseCGroupMemoryLimitForHeap
        -Djava.security.egd=file:/dev/./urandom
      
      # CPU Monitor Agent配置
      AGENT_OPTS: "interval=3000,threshold=60,topMethods=20"
      
      # 应用配置
      SPRING_PROFILES_ACTIVE: "production"
      
    volumes:
      # 监控数据持久化
      - ./monitoring-data:/app/monitoring
      # 应用日志
      - ./app-logs:/app/logs
      # 可选：配置文件
      - ./config:/app/config:ro
      
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    
    # 重启策略
    restart: unless-stopped
    
    # 网络
    networks:
      - app-network

  # 可选：监控数据可视化服务
  monitoring-dashboard:
    image: nginx:alpine
    container_name: monitoring-dashboard
    ports:
      - "8081:80"
    volumes:
      - ./monitoring-data/reports:/usr/share/nginx/html/reports:ro
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - java-app-with-monitor
    networks:
      - app-network

  # 可选：日志收集服务
  log-collector:
    image: fluent/fluent-bit:latest
    container_name: log-collector
    volumes:
      - ./monitoring-data/logs:/var/log/monitoring:ro
      - ./app-logs:/var/log/app:ro
      - ./fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
    depends_on:
      - java-app-with-monitor
    networks:
      - app-network

networks:
  app-network:
    driver: bridge

volumes:
  monitoring-data:
    driver: local
  app-logs:
    driver: local
