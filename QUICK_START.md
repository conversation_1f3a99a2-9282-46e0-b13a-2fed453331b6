# Java CPU Monitor Agent - 快速开始

## 🚀 3分钟快速体验

### 方式一：交互式快速开始（推荐新手）
```bash
git clone https://github.com/YOUR_USERNAME/java-cpu-monitor-agent.git
cd java-cpu-monitor-agent
./quick-start.sh  # 跟随提示操作
```

### 方式二：直接运行演示
```bash
git clone https://github.com/YOUR_USERNAME/java-cpu-monitor-agent.git
cd java-cpu-monitor-agent
./build.sh && ./run-demo.sh
```

## 📱 监控您的Java应用

### 启动时加载Agent
```bash
java -javaagent:java-cpu-monitor-agent.jar -jar your-app.jar
```

### 动态附加到运行中的进程
```bash
# 查看Java进程
jps

# 附加Agent
java -jar java-cpu-monitor-agent.jar <进程ID>
```

## ⚙️ 常用配置

```bash
# 开发环境 - 高频监控
java -javaagent:java-cpu-monitor-agent.jar=interval=2000,threshold=50 -jar your-app.jar

# 生产环境 - 标准监控  
java -javaagent:java-cpu-monitor-agent.jar=interval=10000,threshold=80 -jar your-app.jar
```

## 📊 监控效果预览

```
================================================================================
CPU Performance Analysis Report - 2024-06-12 15:30:25
================================================================================
CPU Usage: Current=75.23%, Max=89.45%, Avg=68.12% (Samples: 120)

Top 5 CPU-Intensive Methods:
--------------------------------------------------------------------------------
com.example.service.DataProcessor.processLargeDataSet()     1250 calls    2847.32ms total   2.28ms avg
com.example.util.StringUtils.performComplexRegex()          890 calls    1923.45ms total   2.16ms avg
com.example.algorithm.SortingAlgorithm.quickSort()          456 calls    1456.78ms total   3.19ms avg

💡 性能建议:
⚠️ 当前CPU使用率过高 (75.23%)，建议检查热点方法
🔥 热点方法: DataProcessor.processLargeDataSet() 占用15.67%的CPU时间
================================================================================
```

## 🐳 Docker环境

```bash
# 监控运行中的容器
./attach-agent.sh your-container-name

# 或使用Docker Compose
docker-compose up
```

## ❓ 遇到问题？

| 问题 | 解决方案 |
|------|----------|
| Agent无法启动 | 检查Java版本(需要8+)和JAR文件路径 |
| 无监控输出 | 降低threshold参数值(如threshold=30) |
| 性能影响大 | 增加interval参数值(如interval=10000) |

## 📚 更多信息

- 📖 [完整文档](README.md)
- 🐛 [问题反馈](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/issues)
- 💬 [讨论区](https://github.com/YOUR_USERNAME/java-cpu-monitor-agent/discussions)

---

**🎉 开始监控您的Java应用性能吧！**
