#!/bin/bash

# Java CPU Monitor Agent 构建脚本

set -e

echo "=========================================="
echo "Java CPU Monitor Agent Build Script"
echo "=========================================="

# 检查Java版本
echo "检查Java环境..."
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装Java 8或更高版本"
    exit 1
fi

JAVA_VERSION=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1-2)
echo "Java版本: $JAVA_VERSION"

# 检查Maven
echo "检查Maven环境..."
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请安装Maven 3.6或更高版本"
    exit 1
fi

MVN_VERSION=$(mvn -version | head -n1 | cut -d' ' -f3)
echo "Maven版本: $MVN_VERSION"

# 清理之前的构建
echo "清理之前的构建..."
mvn clean

# 编译项目
echo "编译项目..."
mvn compile

# 运行测试
echo "运行测试..."
mvn test -q

# 打包项目
echo "打包项目..."
mvn package -q

# 检查生成的JAR文件
AGENT_JAR="target/java-cpu-monitor-agent-1.0.0.jar"
if [ -f "$AGENT_JAR" ]; then
    echo "✅ Agent JAR文件构建成功: $AGENT_JAR"
    echo "文件大小: $(du -h $AGENT_JAR | cut -f1)"
else
    echo "❌ Agent JAR文件构建失败"
    exit 1
fi

# 检查测试类
TEST_CLASSES="target/test-classes/com/monitor/agent/test/TestApplication.class"
if [ -f "$TEST_CLASSES" ]; then
    echo "✅ 测试类编译成功"
else
    echo "❌ 测试类编译失败"
    exit 1
fi

echo ""
echo "=========================================="
echo "构建完成！"
echo "=========================================="
echo ""
echo "使用方法："
echo "1. 启动时加载Agent:"
echo "   java -javaagent:$AGENT_JAR=interval=5000,threshold=70 \\"
echo "        -cp target/test-classes \\"
echo "        com.monitor.agent.test.TestApplication"
echo ""
echo "2. 监控其他Java应用:"
echo "   java -javaagent:$AGENT_JAR=interval=3000,threshold=80 \\"
echo "        -jar your-application.jar"
echo ""
echo "配置参数说明："
echo "- interval: CPU采样间隔(毫秒), 默认5000"
echo "- threshold: CPU阈值(百分比), 默认70"
echo "- topMethods: 显示热点方法数量, 默认10"
echo ""
