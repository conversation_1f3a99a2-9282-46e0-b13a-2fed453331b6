# Java CPU Monitor Agent

[![CI/CD Pipeline](https://github.com/helloworld9999/java-cpu-monitor-agent/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/helloworld9999/java-cpu-monitor-agent/actions)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Java Version](https://img.shields.io/badge/Java-8%2B-blue.svg)](https://www.oracle.com/java/)
[![Docker](https://img.shields.io/badge/Docker-Supported-blue.svg)](https://www.docker.com/)
[![Release](https://img.shields.io/github/v/release/helloworld9999/java-cpu-monitor-agent)](https://github.com/helloworld9999/java-cpu-monitor-agent/releases)

一个基于Java Agent技术的CPU性能监控和分析工具，能够实时监控Java应用程序的CPU使用情况，识别热点方法，并提供详细的性能分析报告。

## 📋 目录

- [⚡ 立即开始](#-立即开始) - 30秒快速体验
- [🌟 为什么选择这个工具？](#-为什么选择这个工具)
- [📊 实时监控效果预览](#-实时监控效果预览)
- [🎯 适用场景](#-适用场景)
- [🛠️ 安装和构建](#️-安装和构建)
- [📖 使用指南](#-使用指南)
- [❓ 常见问题](#-常见问题)
- [🤝 贡献](#-贡献)

> 📖 **只想快速上手？** 查看 [QUICK_START.md](QUICK_START.md) 获得3分钟快速体验指南！

## ⚡ 立即开始

> 🔰 **新手推荐**: 使用 `./quick-start.sh` 交互式脚本，3分钟内完成体验！

**🎯 超级简单 - 一键体验：**
```bash
git clone https://github.com/helloworld9999/java-cpu-monitor-agent.git && cd java-cpu-monitor-agent
./quick-start.sh  # 交互式快速开始！
```

**🚀 或者直接运行演示：**
```bash
git clone https://github.com/helloworld9999/java-cpu-monitor-agent.git && cd java-cpu-monitor-agent
./build.sh && ./run-demo.sh  # 立即看到CPU监控效果！
```

**📱 监控您的Java应用：**
```bash
java -javaagent:java-cpu-monitor-agent.jar -jar your-app.jar
```

## 🚀 详细快速开始

### 立即体验（推荐新手）
```bash
# 1. 克隆项目并构建
git clone https://github.com/helloworld9999/java-cpu-monitor-agent.git
cd java-cpu-monitor-agent
./build.sh

# 2. 运行内置演示（立即看到效果）
./run-demo.sh
```

### 监控您的Java应用
```bash
# 方式一：启动时加载Agent
java -javaagent:java-cpu-monitor-agent.jar=interval=5000,threshold=70 \
     -jar your-application.jar

# 方式二：动态附加到运行中的进程
java -jar java-cpu-monitor-agent.jar <进程ID>
```

### Docker容器监控
```bash
# 监控运行中的Docker容器（零停机）
./attach-agent.sh your-container-name
```

### 直接下载使用
```bash
# 下载预编译版本
wget https://github.com/helloworld9999/java-cpu-monitor-agent/releases/download/v1.0.0/java-cpu-monitor-agent.jar

# 或下载完整发布包
wget https://github.com/helloworld9999/java-cpu-monitor-agent/releases/download/v1.0.0/java-cpu-monitor-agent-v1.0.0.zip
unzip java-cpu-monitor-agent-v1.0.0.zip
cd java-cpu-monitor-agent-v1.0.0

# 系统级安装（可选）
sudo ./install.sh
```

## 🌟 为什么选择这个工具？

- **🔥 零侵入** - 无需修改一行代码，直接监控现有应用
- **⚡ 即时生效** - 30秒内开始监控，立即看到CPU分析报告
- **🐳 Docker友好** - 支持容器环境，可动态附加到运行中的容器
- **📊 智能分析** - 自动识别CPU热点方法，提供优化建议
- **🎯 生产就绪** - 低开销设计，适合生产环境使用
- **🔧 高度可配置** - 灵活的参数配置，适应不同监控需求

## 📊 实时监控效果预览

```bash
================================================================================
CPU Performance Analysis Report - 2024-06-12 15:30:25
================================================================================
CPU Usage: Current=75.23%, Max=89.45%, Avg=68.12% (Samples: 120)

Top 5 CPU-Intensive Methods:
--------------------------------------------------------------------------------
com.example.service.DataProcessor.processLargeDataSet()     1250 calls    2847.32ms total   2.28ms avg
com.example.util.StringUtils.performComplexRegex()          890 calls    1923.45ms total   2.16ms avg
com.example.algorithm.SortingAlgorithm.quickSort()          456 calls    1456.78ms total   3.19ms avg

💡 性能建议:
⚠️ 当前CPU使用率过高 (75.23%)，建议检查热点方法
🔥 热点方法: DataProcessor.processLargeDataSet() 占用15.67%的CPU时间
================================================================================
```

## 🎯 适用场景

| 场景 | 使用方式 | 配置建议 |
|------|----------|----------|
| **🔍 性能问题排查** | `java -javaagent:agent.jar=interval=2000,threshold=50` | 高频采样，快速定位问题 |
| **📈 生产环境监控** | `java -javaagent:agent.jar=interval=10000,threshold=80` | 低频采样，最小化影响 |
| **🐳 Docker容器监控** | `./attach-agent.sh container-name` | 动态附加，零停机监控 |
| **🧪 性能测试分析** | `java -javaagent:agent.jar=interval=1000,threshold=60` | 详细监控，全面分析 |

## 💻 支持的环境

- ✅ **Java 8-21** - 全版本兼容，Java 11+性能优化
- ✅ **Spring Boot** - 完美支持Spring Boot应用
- ✅ **Docker/Kubernetes** - 容器化环境友好
- ✅ **微服务架构** - 支持分布式应用监控
- ✅ **生产环境** - 低开销设计，适合7x24小时运行

## 🛠️ 安装和构建

### 快速构建
```bash
# 克隆项目
git clone https://github.com/helloworld9999/java-cpu-monitor-agent.git
cd java-cpu-monitor-agent

# 一键构建（推荐）
./build.sh

# 或者使用Maven
mvn clean package
```

### Java 11+ 优化构建
```bash
# Java 11优化构建（更好的性能）
./build-java11.sh
```

构建完成后，Agent JAR文件位于：`target/java-cpu-monitor-agent-1.0.0.jar`

### 📁 项目文件说明

| 文件 | 用途 |
|------|------|
| `quick-start.sh` | 🎯 **交互式快速开始脚本** (推荐新手) |
| `run-demo.sh` | 🎬 运行内置演示应用 |
| `build.sh` | 🔨 通用构建脚本 (Java 8+) |
| `build-java11.sh` | ⚡ Java 11+ 优化构建脚本 |
| `attach-agent.sh` | 🐳 Docker容器动态附加脚本 |

## 📖 使用指南

### 1. 体验演示（推荐新手）
```bash
# 运行内置演示，立即看到监控效果
./run-demo.sh
```

### 2. 监控您的Java应用

#### 启动时加载Agent（推荐）
```bash
# 基本用法
java -javaagent:java-cpu-monitor-agent.jar=interval=5000,threshold=70 \
     -jar your-application.jar

# Java 11+ 优化版本
java --add-opens java.management/sun.management=ALL-UNNAMED \
     -XX:+UseG1GC -XX:+UseStringDeduplication \
     -javaagent:java-cpu-monitor-agent.jar=interval=5000,threshold=70 \
     -jar your-application.jar
```

#### 动态附加到运行中的进程
```bash
# 获取Java进程ID
jps

# 动态附加Agent
java -jar java-cpu-monitor-agent.jar <进程ID>
```

### 3. 配置参数

| 参数 | 默认值 | 说明 | 推荐值 |
|------|--------|------|--------|
| `interval` | 5000 | CPU采样间隔（毫秒） | 开发:2000, 生产:10000 |
| `threshold` | 70 | CPU使用率阈值（百分比） | 开发:50, 生产:80 |
| `topMethods` | 10 | 显示的热点方法数量 | 5-20 |

**配置示例：**
```bash
# 开发环境 - 高频监控
-javaagent:java-cpu-monitor-agent.jar=interval=2000,threshold=50,topMethods=15

# 生产环境 - 标准监控
-javaagent:java-cpu-monitor-agent.jar=interval=10000,threshold=80,topMethods=10
```

## 输出示例

### 控制台输出
```
================================================================================
CPU Performance Analysis Report - 2024-01-15 14:30:25
================================================================================
CPU Usage: Current=75.23%, Max=89.45%, Avg=68.12% (Samples: 120)

Top 10 CPU-Intensive Methods:
--------------------------------------------------------------------------------
com.example.service.DataProcessor.processLargeDataSet()     1250 calls    2847.32ms total   2.28ms avg
com.example.util.StringUtils.performComplexRegex()          890 calls    1923.45ms total   2.16ms avg
com.example.algorithm.SortingAlgorithm.quickSort()          456 calls    1456.78ms total   3.19ms avg
com.example.math.Calculator.performComplexCalculation()     2340 calls    1234.56ms total   0.53ms avg
================================================================================
```

### JSON报告格式
```json
{
  "timestamp": "2024-01-15 14:30:25",
  "reportType": "CPU Performance Analysis",
  "version": "1.0.0",
  "cpuMetrics": {
    "currentUsage": "75.23%",
    "maxUsage": "89.45%",
    "avgUsage": "68.12%",
    "sampleCount": 120,
    "recentTrend": ["70.12%", "72.34%", "75.23%"]
  },
  "topMethods": [
    {
      "methodSignature": "com.example.service.DataProcessor.processLargeDataSet()",
      "callCount": 1250,
      "totalDurationMs": "2847.32",
      "avgDurationMs": "2.28",
      "cpuUsagePercent": "15.67%"
    }
  ],
  "summary": {
    "totalMethodCalls": 45678,
    "uniqueMethodsCount": 234,
    "recommendations": [
      "🔥 热点方法: com.example.service.DataProcessor.processLargeDataSet() (总耗时: 2847.32ms)",
      "⚠️ 当前CPU使用率过高 (75.23%)，建议检查热点方法"
    ]
  }
}
```

## Java 11+ 特性优化

### 🚀 性能优化
- **String.repeat()** - 使用Java 11原生字符串重复方法，性能更优
- **G1垃圾收集器** - 推荐使用G1GC，减少监控开销
- **字符串去重** - 启用字符串去重功能，降低内存使用

### 🔧 JVM参数优化
```bash
# Java 11+ 推荐JVM参数
java --add-opens java.management/sun.management=ALL-UNNAMED \
     -XX:+UseG1GC \
     -XX:+UseStringDeduplication \
     -XX:MaxGCPauseMillis=200 \
     -javaagent:java-cpu-monitor-agent.jar \
     YourApplication
```

### 📊 模块系统兼容
- 完全兼容Java 9+ 模块系统
- 使用`--add-opens`参数访问JDK内部API
- 支持模块化应用监控

## 高级用法

### 自定义过滤规则

Agent默认会忽略JDK内部类和常见的getter/setter方法。如需自定义过滤规则，可以修改`ClassTransformer`类：

```java
// 在ClassTransformer.java中添加自定义过滤规则
.ignore(ElementMatchers.nameStartsWith("com.yourcompany.ignored."))
```

### 性能调优建议

1. **采样间隔调整**
   - 高频监控：`interval=1000`（1秒）
   - 常规监控：`interval=5000`（5秒，推荐）
   - 低频监控：`interval=10000`（10秒）

2. **阈值设置**
   - 开发环境：`threshold=50`
   - 测试环境：`threshold=70`
   - 生产环境：`threshold=80`

3. **方法过滤**
   - 避免监控简单的getter/setter方法
   - 专注于业务逻辑复杂的方法
   - 可以通过修改字节码转换规则来精确控制

## ❓ 常见问题

### 快速解决方案

| 问题 | 解决方案 |
|------|----------|
| **Agent无法启动** | 检查Java版本(需要8+)和JAR文件路径 |
| **无监控输出** | 降低threshold参数值(如threshold=30) |
| **性能影响大** | 增加interval参数值(如interval=10000) |
| **权限错误** | 添加JVM参数：`-Djdk.attach.allowAttachSelf=true` |

### 调试模式
```bash
# 启用详细日志
java -Dcom.monitor.agent.debug=true -javaagent:java-cpu-monitor-agent.jar YourApp
```

### 获取帮助
- 🐛 [提交Issue](https://github.com/helloworld9999/java-cpu-monitor-agent/issues)
- 💬 [讨论区](https://github.com/helloworld9999/java-cpu-monitor-agent/discussions)
- 📖 查看完整文档

## 架构设计

### 核心组件

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   CpuProfiler   │    │  ClassTransformer │    │  DataCollector  │
│                 │    │                  │    │                 │
│ - CPU采样       │    │ - 字节码增强      │    │ - 数据收集      │
│ - 线程监控      │    │ - 方法拦截        │    │ - 指标计算      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ ReportGenerator │
                    │                 │
                    │ - 报告生成      │
                    │ - 格式化输出    │
                    └─────────────────┘
```

### 技术栈
- **Java Agent API** - 字节码插桩入口
- **ByteBuddy** - 高性能字节码操作库
- **ThreadMXBean** - JVM内置线程监控
- **Jackson** - JSON序列化和反序列化
- **Maven** - 项目构建和依赖管理

## 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
git clone <repository-url>
cd java-monitor-agent
mvn clean compile
```

### 运行测试
```bash
mvn test
```

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

我们欢迎各种形式的贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解详细信息。

### 快速开始贡献
1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [GitHub仓库](https://github.com/helloworld9999/java-cpu-monitor-agent)
- [问题反馈](https://github.com/helloworld9999/java-cpu-monitor-agent/issues)
- [发布页面](https://github.com/helloworld9999/java-cpu-monitor-agent/releases)
- [讨论区](https://github.com/helloworld9999/java-cpu-monitor-agent/discussions)

## ⭐ Star History

如果这个项目对您有帮助，请给我们一个 ⭐！

[![Star History Chart](https://api.star-history.com/svg?repos=helloworld9999/java-cpu-monitor-agent&type=Date)](https://star-history.com/#helloworld9999/java-cpu-monitor-agent&Date)

## 更新日志

查看 [CHANGELOG.md](CHANGELOG.md) 了解详细的版本更新信息。

### v1.0.0 (2024-06-12)
- 🎉 首次发布
- ✅ 实现基础CPU监控功能
- ✅ 支持热点方法识别
- ✅ 提供JSON和控制台报告格式
- ✅ 支持动态配置参数
- ✅ 完整的Docker部署支持
