package com.monitor.agent.reporter;

/**
 * 控制台报告输出器
 * 负责将性能报告输出到控制台
 */
public class ConsoleReporter {
    
    private static final String ANSI_RESET = "\u001B[0m";
    private static final String ANSI_RED = "\u001B[31m";
    private static final String ANSI_GREEN = "\u001B[32m";
    private static final String ANSI_YELLOW = "\u001B[33m";
    private static final String ANSI_BLUE = "\u001B[34m";
    private static final String ANSI_PURPLE = "\u001B[35m";
    private static final String ANSI_CYAN = "\u001B[36m";
    private static final String ANSI_BOLD = "\u001B[1m";
    
    /**
     * 打印性能报告到控制台
     */
    public static void printReport(String report) {
        System.out.println(colorizeReport(report));
    }
    
    /**
     * 打印带颜色的警告信息
     */
    public static void printWarning(String message) {
        System.out.println(ANSI_YELLOW + ANSI_BOLD + "[CPU Monitor Agent WARNING] " + ANSI_RESET + ANSI_YELLOW + message + ANSI_RESET);
    }
    
    /**
     * 打印带颜色的错误信息
     */
    public static void printError(String message) {
        System.err.println(ANSI_RED + ANSI_BOLD + "[CPU Monitor Agent ERROR] " + ANSI_RESET + ANSI_RED + message + ANSI_RESET);
    }
    
    /**
     * 打印带颜色的信息
     */
    public static void printInfo(String message) {
        System.out.println(ANSI_BLUE + "[CPU Monitor Agent INFO] " + ANSI_RESET + message);
    }
    
    /**
     * 打印带颜色的成功信息
     */
    public static void printSuccess(String message) {
        System.out.println(ANSI_GREEN + ANSI_BOLD + "[CPU Monitor Agent SUCCESS] " + ANSI_RESET + ANSI_GREEN + message + ANSI_RESET);
    }
    
    /**
     * 为报告添加颜色
     */
    private static String colorizeReport(String report) {
        if (!isColorSupported()) {
            return report;
        }
        
        // 为不同类型的内容添加颜色
        String colorizedReport = report
            // 标题和分隔线
            .replaceAll("={80}", ANSI_CYAN + ANSI_BOLD + "=" + repeatString("=", 79) + ANSI_RESET)
            .replaceAll("-{80}", ANSI_BLUE + repeatString("-", 80) + ANSI_RESET)

            // 方法名
            .replaceAll("(\\w+\\.\\w+\\.\\w+\\([^)]*\\))", ANSI_PURPLE + "$1" + ANSI_RESET)

            // 数字
            .replaceAll("([0-9]+\\.[0-9]+ms)", ANSI_YELLOW + "$1" + ANSI_RESET)
            .replaceAll("([0-9]+ calls)", ANSI_GREEN + "$1" + ANSI_RESET)

            // 报告标题
            .replaceAll("CPU Performance Analysis Report",
                ANSI_BOLD + ANSI_CYAN + "CPU Performance Analysis Report" + ANSI_RESET);

        return colorizedReport;
    }

    /**
     * 重复字符串 (Java 11优化版本)
     */
    private static String repeatString(String str, int count) {
        return str.repeat(count);
    }
    
    /**
     * 根据百分比值返回相应颜色的文本
     */
    private static String colorizePercentage(double percentage) {
        String color;
        if (percentage >= 80) {
            color = ANSI_RED + ANSI_BOLD;  // 高CPU使用率 - 红色加粗
        } else if (percentage >= 60) {
            color = ANSI_YELLOW;           // 中等CPU使用率 - 黄色
        } else {
            color = ANSI_GREEN;            // 低CPU使用率 - 绿色
        }
        
        return color + String.format("%.2f", percentage) + ANSI_RESET;
    }
    
    /**
     * 从匹配的文本中提取百分比数值
     */
    private static double extractPercentage(String match) {
        try {
            String percentStr = match.replaceAll(".*Current=([0-9.]+)%.*", "$1");
            return Double.parseDouble(percentStr);
        } catch (Exception e) {
            return 0.0;
        }
    }
    
    /**
     * 检查终端是否支持颜色输出
     */
    private static boolean isColorSupported() {
        // 检查环境变量
        String term = System.getenv("TERM");
        String colorTerm = System.getenv("COLORTERM");
        
        // 如果明确禁用颜色
        String noColor = System.getenv("NO_COLOR");
        if (noColor != null && !noColor.isEmpty()) {
            return false;
        }
        
        // 检查是否在支持颜色的终端中
        return (term != null && (term.contains("color") || term.contains("xterm"))) ||
               (colorTerm != null && !colorTerm.isEmpty()) ||
               System.getProperty("os.name").toLowerCase().contains("windows");
    }
    
    /**
     * 打印简化的实时状态
     */
    public static void printRealTimeStatus(double cpuUsage, int activeThreads, long totalMethodCalls) {
        String status = String.format("\r[CPU Monitor] CPU: %s | Threads: %d | Method Calls: %,d",
            colorizePercentage(cpuUsage) + "%",
            activeThreads,
            totalMethodCalls);
        
        System.out.print(status);
    }
    
    /**
     * 清除当前行（用于实时状态更新）
     */
    public static void clearLine() {
        System.out.print("\r" + " ".repeat(100) + "\r");
    }
    
    /**
     * 打印启动横幅
     */
    public static void printBanner() {
        String banner = 
            ANSI_CYAN + ANSI_BOLD +
            "\n" +
            "  ╔═══════════════════════════════════════════════════════════════╗\n" +
            "  ║                    Java CPU Monitor Agent                     ║\n" +
            "  ║                     Version 1.0.0                            ║\n" +
            "  ║              Real-time CPU Performance Analysis              ║\n" +
            "  ╚═══════════════════════════════════════════════════════════════╝\n" +
            ANSI_RESET;
        
        System.out.println(banner);
    }
    
    /**
     * 打印关闭信息
     */
    public static void printShutdownMessage() {
        String message = 
            ANSI_YELLOW + ANSI_BOLD +
            "\n" +
            "  ╔═══════════════════════════════════════════════════════════════╗\n" +
            "  ║                  CPU Monitor Agent Shutdown                   ║\n" +
            "  ║                   Thank you for using!                       ║\n" +
            "  ╚═══════════════════════════════════════════════════════════════╝\n" +
            ANSI_RESET;
        
        System.out.println(message);
    }
}
