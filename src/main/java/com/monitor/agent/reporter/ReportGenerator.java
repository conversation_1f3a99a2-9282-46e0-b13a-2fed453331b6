package com.monitor.agent.reporter;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.monitor.agent.collector.DataCollector;
import com.monitor.agent.profiler.CpuMetrics;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 报告生成器
 * 负责生成CPU性能分析报告
 */
public class ReportGenerator {
    
    private final ObjectMapper objectMapper;
    private final int topMethodsCount;
    private final SimpleDateFormat dateFormat;
    
    public ReportGenerator(int topMethodsCount) {
        this.topMethodsCount = topMethodsCount;
        this.objectMapper = new ObjectMapper();
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    }
    
    /**
     * 生成完整的性能报告
     */
    public String generateReport(CpuMetrics cpuMetrics, Map<String, DataCollector.MethodMetrics> methodMetrics) {
        try {
            ObjectNode report = objectMapper.createObjectNode();
            
            // 报告基本信息
            report.put("timestamp", dateFormat.format(new Date()));
            report.put("reportType", "CPU Performance Analysis");
            report.put("version", "1.0.0");
            
            // CPU指标
            ObjectNode cpuSection = generateCpuSection(cpuMetrics);
            report.set("cpuMetrics", cpuSection);
            
            // 方法性能指标
            ArrayNode methodsSection = generateMethodsSection(methodMetrics);
            report.set("topMethods", methodsSection);
            
            // 线程信息
            ArrayNode threadsSection = generateThreadsSection(cpuMetrics);
            report.set("topThreads", threadsSection);
            
            // 生成摘要
            ObjectNode summary = generateSummary(cpuMetrics, methodMetrics);
            report.set("summary", summary);
            
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(report);
            
        } catch (Exception e) {
            return generateErrorReport(e);
        }
    }
    
    /**
     * 生成CPU指标部分
     */
    private ObjectNode generateCpuSection(CpuMetrics cpuMetrics) {
        ObjectNode cpuSection = objectMapper.createObjectNode();
        
        cpuSection.put("currentUsage", String.format("%.2f%%", cpuMetrics.getCurrentProcessCpuUsage()));
        cpuSection.put("maxUsage", String.format("%.2f%%", cpuMetrics.getMaxProcessCpuUsage()));
        cpuSection.put("avgUsage", String.format("%.2f%%", cpuMetrics.getAvgProcessCpuUsage()));
        cpuSection.put("sampleCount", cpuMetrics.getSampleCount());
        
        // CPU使用率趋势
        List<Double> trend = cpuMetrics.getCpuUsageTrend(10);
        ArrayNode trendArray = objectMapper.createArrayNode();
        for (Double usage : trend) {
            trendArray.add(String.format("%.2f%%", usage));
        }
        cpuSection.set("recentTrend", trendArray);
        
        return cpuSection;
    }
    
    /**
     * 生成方法性能指标部分
     */
    private ArrayNode generateMethodsSection(Map<String, DataCollector.MethodMetrics> methodMetrics) {
        ArrayNode methodsArray = objectMapper.createArrayNode();
        
        // 获取CPU使用率最高的方法
        List<DataCollector.MethodMetrics> topMethods = methodMetrics.values().stream()
            .sorted((a, b) -> Long.compare(b.getTotalDuration(), a.getTotalDuration()))
            .limit(topMethodsCount)
            .collect(java.util.stream.Collectors.toList());
        
        for (DataCollector.MethodMetrics metrics : topMethods) {
            ObjectNode methodNode = objectMapper.createObjectNode();
            
            methodNode.put("methodSignature", metrics.getMethodSignature());
            methodNode.put("callCount", metrics.getCallCount());
            methodNode.put("totalDurationMs", String.format("%.2f", metrics.getTotalDuration() / 1_000_000.0));
            methodNode.put("avgDurationMs", String.format("%.2f", metrics.getAvgDuration() / 1_000_000.0));
            methodNode.put("minDurationMs", String.format("%.2f", metrics.getMinDuration() / 1_000_000.0));
            methodNode.put("maxDurationMs", String.format("%.2f", metrics.getMaxDuration() / 1_000_000.0));
            methodNode.put("exceptionCount", metrics.getExceptionCount());
            
            // 计算相对CPU占用百分比
            long totalDuration = methodMetrics.values().stream()
                .mapToLong(DataCollector.MethodMetrics::getTotalDuration)
                .sum();
            
            if (totalDuration > 0) {
                double cpuPercent = (double) metrics.getTotalDuration() / totalDuration * 100.0;
                methodNode.put("cpuUsagePercent", String.format("%.2f%%", cpuPercent));
            } else {
                methodNode.put("cpuUsagePercent", "0.00%");
            }
            
            methodsArray.add(methodNode);
        }
        
        return methodsArray;
    }
    
    /**
     * 生成线程信息部分
     */
    private ArrayNode generateThreadsSection(CpuMetrics cpuMetrics) {
        ArrayNode threadsArray = objectMapper.createArrayNode();
        
        // 获取CPU使用率最高的线程
        List<CpuMetrics.ThreadCpuMetrics> topThreads = cpuMetrics.getTopCpuThreads(10);
        
        for (CpuMetrics.ThreadCpuMetrics threadMetrics : topThreads) {
            ObjectNode threadNode = objectMapper.createObjectNode();
            
            threadNode.put("threadId", threadMetrics.getThreadId());
            threadNode.put("threadName", threadMetrics.getThreadName());
            threadNode.put("threadState", threadMetrics.getThreadState());
            threadNode.put("currentCpuUsage", String.format("%.2f%%", threadMetrics.getCurrentCpuUsage()));
            threadNode.put("maxCpuUsage", String.format("%.2f%%", threadMetrics.getMaxCpuUsage()));
            threadNode.put("avgCpuUsage", String.format("%.2f%%", threadMetrics.getAvgCpuUsage()));
            threadNode.put("sampleCount", threadMetrics.getSampleCount());
            
            threadsArray.add(threadNode);
        }
        
        return threadsArray;
    }
    
    /**
     * 生成报告摘要
     */
    private ObjectNode generateSummary(CpuMetrics cpuMetrics, Map<String, DataCollector.MethodMetrics> methodMetrics) {
        ObjectNode summary = objectMapper.createObjectNode();
        
        // 总体统计
        long totalMethodCalls = methodMetrics.values().stream()
            .mapToLong(DataCollector.MethodMetrics::getCallCount)
            .sum();
        
        long totalExecutionTime = methodMetrics.values().stream()
            .mapToLong(DataCollector.MethodMetrics::getTotalDuration)
            .sum();
        
        summary.put("totalMethodCalls", totalMethodCalls);
        summary.put("totalExecutionTimeMs", String.format("%.2f", totalExecutionTime / 1_000_000.0));
        summary.put("uniqueMethodsCount", methodMetrics.size());
        summary.put("currentCpuUsage", String.format("%.2f%%", cpuMetrics.getCurrentProcessCpuUsage()));
        
        // 性能建议
        ArrayNode recommendations = generateRecommendations(cpuMetrics, methodMetrics);
        summary.set("recommendations", recommendations);
        
        return summary;
    }
    
    /**
     * 生成性能优化建议
     */
    private ArrayNode generateRecommendations(CpuMetrics cpuMetrics, Map<String, DataCollector.MethodMetrics> methodMetrics) {
        ArrayNode recommendations = objectMapper.createArrayNode();
        
        // 高CPU使用率警告
        if (cpuMetrics.getCurrentProcessCpuUsage() > 80) {
            recommendations.add("⚠️ 当前CPU使用率过高 (" + String.format("%.2f%%", cpuMetrics.getCurrentProcessCpuUsage()) + ")，建议检查热点方法");
        }
        
        // 热点方法建议
        List<DataCollector.MethodMetrics> topMethods = methodMetrics.values().stream()
            .sorted((a, b) -> Long.compare(b.getTotalDuration(), a.getTotalDuration()))
            .limit(3)
            .collect(java.util.stream.Collectors.toList());
        
        if (!topMethods.isEmpty()) {
            DataCollector.MethodMetrics topMethod = topMethods.get(0);
            recommendations.add("🔥 热点方法: " + topMethod.getMethodSignature() + 
                " (总耗时: " + String.format("%.2f", topMethod.getTotalDuration() / 1_000_000.0) + "ms)");
        }
        
        // 异常频率建议
        long totalExceptions = methodMetrics.values().stream()
            .mapToLong(DataCollector.MethodMetrics::getExceptionCount)
            .sum();
        
        if (totalExceptions > 0) {
            recommendations.add("⚡ 检测到 " + totalExceptions + " 个异常，建议检查异常处理逻辑");
        }
        
        // 方法调用频率建议
        methodMetrics.values().stream()
            .filter(m -> m.getCallCount() > 10000)
            .forEach(m -> recommendations.add("📈 高频调用方法: " + m.getMethodSignature() + 
                " (调用次数: " + m.getCallCount() + ")"));
        
        return recommendations;
    }
    
    /**
     * 生成错误报告
     */
    private String generateErrorReport(Exception e) {
        try {
            ObjectNode errorReport = objectMapper.createObjectNode();
            errorReport.put("timestamp", dateFormat.format(new Date()));
            errorReport.put("error", "Failed to generate report");
            errorReport.put("message", e.getMessage());
            errorReport.put("type", e.getClass().getSimpleName());
            
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(errorReport);
        } catch (Exception ex) {
            return "{\n  \"error\": \"Failed to generate report\",\n  \"message\": \"" + e.getMessage() + "\"\n}";
        }
    }
    
    /**
     * 生成简化的控制台报告
     */
    public String generateConsoleReport(CpuMetrics cpuMetrics, Map<String, DataCollector.MethodMetrics> methodMetrics) {
        StringBuilder sb = new StringBuilder();
        
        sb.append("\n").append("=".repeat(80)).append("\n");
        sb.append("CPU Performance Analysis Report - ").append(dateFormat.format(new Date())).append("\n");
        sb.append("=".repeat(80)).append("\n");
        
        // CPU指标
        sb.append(String.format("CPU Usage: Current=%.2f%%, Max=%.2f%%, Avg=%.2f%% (Samples: %d)\n",
            cpuMetrics.getCurrentProcessCpuUsage(),
            cpuMetrics.getMaxProcessCpuUsage(),
            cpuMetrics.getAvgProcessCpuUsage(),
            cpuMetrics.getSampleCount()));
        
        sb.append("\nTop ").append(topMethodsCount).append(" CPU-Intensive Methods:\n");
        sb.append("-".repeat(80)).append("\n");
        
        // 热点方法
        methodMetrics.values().stream()
            .sorted((a, b) -> Long.compare(b.getTotalDuration(), a.getTotalDuration()))
            .limit(topMethodsCount)
            .forEach(m -> {
                sb.append(String.format("%-50s %8d calls %10.2fms total %8.2fms avg\n",
                    truncateMethodName(m.getMethodSignature(), 50),
                    m.getCallCount(),
                    m.getTotalDuration() / 1_000_000.0,
                    m.getAvgDuration() / 1_000_000.0));
            });
        
        sb.append("=".repeat(80)).append("\n");
        
        return sb.toString();
    }
    
    /**
     * 截断方法名以适应显示
     */
    private String truncateMethodName(String methodName, int maxLength) {
        if (methodName.length() <= maxLength) {
            return methodName;
        }
        return "..." + methodName.substring(methodName.length() - maxLength + 3);
    }
}
