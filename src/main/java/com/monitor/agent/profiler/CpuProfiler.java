package com.monitor.agent.profiler;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * CPU性能分析器
 * 负责监控系统和线程级别的CPU使用情况
 */
public class CpuProfiler {
    
    private final ThreadMXBean threadMXBean;
    private final double cpuThreshold;
    private final Map<Long, ThreadCpuInfo> threadCpuMap;
    private final CpuMetrics cpuMetrics;
    
    private long lastSystemTime;
    private long lastProcessCpuTime;
    private boolean highCpuDetected;
    
    public CpuProfiler(double cpuThreshold) {
        this.cpuThreshold = cpuThreshold;
        this.threadMXBean = ManagementFactory.getThreadMXBean();
        this.threadCpuMap = new ConcurrentHashMap<>();
        this.cpuMetrics = new CpuMetrics();
        
        // 启用线程CPU时间测量
        if (threadMXBean.isThreadCpuTimeSupported()) {
            threadMXBean.setThreadCpuTimeEnabled(true);
        }
        
        // 初始化基准时间
        this.lastSystemTime = System.nanoTime();
        this.lastProcessCpuTime = getProcessCpuTime();
    }
    
    /**
     * 执行CPU采样
     */
    public void sample() {
        long currentSystemTime = System.nanoTime();
        long currentProcessCpuTime = getProcessCpuTime();
        
        // 计算进程CPU使用率
        double processCpuUsage = calculateProcessCpuUsage(
            currentSystemTime, currentProcessCpuTime
        );
        
        // 更新CPU指标
        cpuMetrics.updateProcessCpuUsage(processCpuUsage);
        
        // 检查是否超过阈值
        highCpuDetected = processCpuUsage > cpuThreshold;
        
        // 如果CPU使用率高，采样线程信息
        if (highCpuDetected) {
            sampleThreads();
        }
        
        // 更新基准时间
        lastSystemTime = currentSystemTime;
        lastProcessCpuTime = currentProcessCpuTime;
    }
    
    /**
     * 采样所有线程的CPU使用情况
     */
    private void sampleThreads() {
        long[] threadIds = threadMXBean.getAllThreadIds();
        
        for (long threadId : threadIds) {
            try {
                long cpuTime = threadMXBean.getThreadCpuTime(threadId);
                if (cpuTime == -1) continue; // 线程已终止
                
                ThreadCpuInfo threadInfo = threadCpuMap.get(threadId);
                if (threadInfo == null) {
                    // 新线程
                    threadInfo = new ThreadCpuInfo(threadId, cpuTime);
                    threadCpuMap.put(threadId, threadInfo);
                } else {
                    // 更新现有线程信息
                    threadInfo.updateCpuTime(cpuTime);
                }
                
                // 获取线程详细信息
                String threadName = getThreadInfo(threadId);
                java.lang.management.ThreadInfo info = threadMXBean.getThreadInfo(threadId);
                String threadState = info != null ? info.getThreadState().toString() : "UNKNOWN";

                // 更新CPU指标中的线程信息
                cpuMetrics.updateThreadCpuUsage(threadId, threadInfo.getCpuUsagePercent(), threadName, threadState);
                
            } catch (Exception e) {
                // 忽略已终止的线程
            }
        }
        
        // 清理已终止的线程
        cleanupTerminatedThreads(threadIds);
    }
    
    /**
     * 计算进程CPU使用率
     */
    private double calculateProcessCpuUsage(long currentSystemTime, long currentProcessCpuTime) {
        if (lastSystemTime == 0 || lastProcessCpuTime == 0) {
            return 0.0;
        }
        
        long systemTimeDelta = currentSystemTime - lastSystemTime;
        long processCpuTimeDelta = currentProcessCpuTime - lastProcessCpuTime;
        
        if (systemTimeDelta <= 0) {
            return 0.0;
        }
        
        // 获取可用处理器数量
        int availableProcessors = Runtime.getRuntime().availableProcessors();
        
        // 计算CPU使用率百分比
        double cpuUsage = (double) processCpuTimeDelta / systemTimeDelta * 100.0;
        
        // 考虑多核情况，将使用率标准化到单核
        return Math.min(cpuUsage / availableProcessors, 100.0);
    }
    
    /**
     * 获取进程CPU时间
     */
    private long getProcessCpuTime() {
        try {
            // 使用反射来避免编译时的访问限制警告
            java.lang.management.OperatingSystemMXBean osBean = ManagementFactory.getOperatingSystemMXBean();
            java.lang.reflect.Method method = osBean.getClass().getMethod("getProcessCpuTime");
            return (Long) method.invoke(osBean);
        } catch (Exception e) {
            // 如果无法获取进程CPU时间，使用所有线程CPU时间之和作为近似值
            return getAllThreadsCpuTime();
        }
    }
    
    /**
     * 获取所有线程CPU时间之和
     */
    private long getAllThreadsCpuTime() {
        long totalCpuTime = 0;
        long[] threadIds = threadMXBean.getAllThreadIds();
        
        for (long threadId : threadIds) {
            try {
                long cpuTime = threadMXBean.getThreadCpuTime(threadId);
                if (cpuTime > 0) {
                    totalCpuTime += cpuTime;
                }
            } catch (Exception e) {
                // 忽略异常
            }
        }
        
        return totalCpuTime;
    }
    
    /**
     * 清理已终止的线程
     */
    private void cleanupTerminatedThreads(long[] activeThreadIds) {
        // 将活跃线程ID转换为Set以便快速查找
        java.util.Set<Long> activeThreadSet = new java.util.HashSet<>();
        for (long threadId : activeThreadIds) {
            activeThreadSet.add(threadId);
        }
        
        // 移除不再活跃的线程
        threadCpuMap.entrySet().removeIf(entry -> !activeThreadSet.contains(entry.getKey()));
    }
    
    /**
     * 获取线程信息
     */
    public String getThreadInfo(long threadId) {
        try {
            java.lang.management.ThreadInfo threadInfo = threadMXBean.getThreadInfo(threadId);
            if (threadInfo != null) {
                return threadInfo.getThreadName() + " (State: " + threadInfo.getThreadState() + ")";
            }
        } catch (Exception e) {
            // 忽略异常
        }
        return "Thread-" + threadId;
    }
    
    // Getter方法
    public boolean isHighCpuDetected() {
        return highCpuDetected;
    }
    
    public CpuMetrics getCpuMetrics() {
        return cpuMetrics;
    }
    
    public double getCpuThreshold() {
        return cpuThreshold;
    }
    
    /**
     * 线程CPU信息类
     */
    private static class ThreadCpuInfo {
        private final long threadId;
        private long lastCpuTime;
        private long lastSampleTime;
        private double cpuUsagePercent;
        
        public ThreadCpuInfo(long threadId, long initialCpuTime) {
            this.threadId = threadId;
            this.lastCpuTime = initialCpuTime;
            this.lastSampleTime = System.nanoTime();
            this.cpuUsagePercent = 0.0;
        }
        
        public void updateCpuTime(long currentCpuTime) {
            long currentSampleTime = System.nanoTime();
            
            if (lastCpuTime > 0 && lastSampleTime > 0) {
                long cpuTimeDelta = currentCpuTime - lastCpuTime;
                long sampleTimeDelta = currentSampleTime - lastSampleTime;
                
                if (sampleTimeDelta > 0) {
                    cpuUsagePercent = (double) cpuTimeDelta / sampleTimeDelta * 100.0;
                }
            }
            
            lastCpuTime = currentCpuTime;
            lastSampleTime = currentSampleTime;
        }
        
        public long getThreadId() { return threadId; }
        public double getCpuUsagePercent() { return cpuUsagePercent; }
        public long getLastCpuTime() { return lastCpuTime; }
    }
}
