package com.monitor.agent.profiler;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * CPU指标数据类
 * 存储和管理CPU相关的性能指标
 */
public class CpuMetrics {
    
    private volatile double currentProcessCpuUsage;
    private volatile double maxProcessCpuUsage;
    private volatile double avgProcessCpuUsage;
    private final List<Double> cpuUsageHistory;
    private final Map<Long, ThreadCpuMetrics> threadMetrics;
    
    private long sampleCount;
    private double totalCpuUsage;
    
    public CpuMetrics() {
        this.cpuUsageHistory = new ArrayList<>();
        this.threadMetrics = new ConcurrentHashMap<>();
        this.sampleCount = 0;
        this.totalCpuUsage = 0.0;
        this.currentProcessCpuUsage = 0.0;
        this.maxProcessCpuUsage = 0.0;
        this.avgProcessCpuUsage = 0.0;
    }
    
    /**
     * 更新进程CPU使用率
     */
    public synchronized void updateProcessCpuUsage(double cpuUsage) {
        currentProcessCpuUsage = cpuUsage;
        
        // 更新最大值
        if (cpuUsage > maxProcessCpuUsage) {
            maxProcessCpuUsage = cpuUsage;
        }
        
        // 更新平均值
        sampleCount++;
        totalCpuUsage += cpuUsage;
        avgProcessCpuUsage = totalCpuUsage / sampleCount;
        
        // 保存历史记录（最多保留100个样本）
        cpuUsageHistory.add(cpuUsage);
        if (cpuUsageHistory.size() > 100) {
            cpuUsageHistory.remove(0);
        }
    }
    
    /**
     * 更新线程CPU使用情况
     */
    public void updateThreadCpuUsage(long threadId, double cpuUsage, String threadName, String threadState) {
        ThreadCpuMetrics metrics = threadMetrics.computeIfAbsent(threadId,
            k -> new ThreadCpuMetrics(threadId));

        metrics.updateCpuUsage(cpuUsage, threadName, threadState);
    }
    
    /**
     * 获取CPU使用率最高的线程
     */
    public List<ThreadCpuMetrics> getTopCpuThreads(int topN) {
        return threadMetrics.values().stream()
            .sorted((a, b) -> Double.compare(b.getCurrentCpuUsage(), a.getCurrentCpuUsage()))
            .limit(topN)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取CPU使用率历史趋势
     */
    public List<Double> getCpuUsageTrend(int lastN) {
        synchronized (this) {
            int size = cpuUsageHistory.size();
            int startIndex = Math.max(0, size - lastN);
            return new ArrayList<>(cpuUsageHistory.subList(startIndex, size));
        }
    }
    
    /**
     * 重置指标
     */
    public synchronized void reset() {
        currentProcessCpuUsage = 0.0;
        maxProcessCpuUsage = 0.0;
        avgProcessCpuUsage = 0.0;
        sampleCount = 0;
        totalCpuUsage = 0.0;
        cpuUsageHistory.clear();
        threadMetrics.clear();
    }
    
    // Getter方法
    public double getCurrentProcessCpuUsage() {
        return currentProcessCpuUsage;
    }
    
    public double getMaxProcessCpuUsage() {
        return maxProcessCpuUsage;
    }
    
    public double getAvgProcessCpuUsage() {
        return avgProcessCpuUsage;
    }
    
    public long getSampleCount() {
        return sampleCount;
    }
    
    public Map<Long, ThreadCpuMetrics> getThreadMetrics() {
        return new ConcurrentHashMap<>(threadMetrics);
    }
    
    /**
     * 线程CPU指标类
     */
    public static class ThreadCpuMetrics {
        private final long threadId;
        private volatile double currentCpuUsage;
        private volatile double maxCpuUsage;
        private volatile double avgCpuUsage;
        private volatile String threadName;
        private volatile String threadState;
        
        private long sampleCount;
        private double totalCpuUsage;
        
        public ThreadCpuMetrics(long threadId) {
            this.threadId = threadId;
            this.currentCpuUsage = 0.0;
            this.maxCpuUsage = 0.0;
            this.avgCpuUsage = 0.0;
            this.sampleCount = 0;
            this.totalCpuUsage = 0.0;
            this.threadName = "Thread-" + threadId;
            this.threadState = "UNKNOWN";
        }
        

        
        public void updateCpuUsage(double cpuUsage, String name, String state) {
            currentCpuUsage = cpuUsage;
            threadName = name != null ? name : threadName;
            threadState = state != null ? state : threadState;
            
            if (cpuUsage > maxCpuUsage) {
                maxCpuUsage = cpuUsage;
            }
            
            sampleCount++;
            totalCpuUsage += cpuUsage;
            avgCpuUsage = totalCpuUsage / sampleCount;
        }
        
        // Getter方法
        public long getThreadId() { return threadId; }
        public double getCurrentCpuUsage() { return currentCpuUsage; }
        public double getMaxCpuUsage() { return maxCpuUsage; }
        public double getAvgCpuUsage() { return avgCpuUsage; }
        public String getThreadName() { return threadName; }
        public String getThreadState() { return threadState; }
        public long getSampleCount() { return sampleCount; }
        
        @Override
        public String toString() {
            return String.format("Thread[%d] %s (State: %s) - Current: %.2f%%, Max: %.2f%%, Avg: %.2f%%",
                threadId, threadName, threadState, currentCpuUsage, maxCpuUsage, avgCpuUsage);
        }
    }
}
