package com.monitor.agent.instrumentation;

import com.monitor.agent.collector.DataCollector;

import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.security.ProtectionDomain;

/**
 * 字节码转换器
 * 使用ByteBuddy对目标类进行字节码增强，插入性能监控代码
 */
public class ClassTransformer implements ClassFileTransformer {

    private final DataCollector dataCollector;

    public ClassTransformer(DataCollector dataCollector) {
        this.dataCollector = dataCollector;
        // 设置DataCollector到全局持有者
        DataCollectorHolder.setInstance(dataCollector);
    }
    
    @Override
    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                          ProtectionDomain protectionDomain, byte[] classfileBuffer)
            throws IllegalClassFormatException {

        // 过滤不需要增强的类
        if (shouldIgnoreClass(className)) {
            return classfileBuffer;
        }

        // 暂时返回原始字节码，避免复杂的字节码操作
        // 在实际生产环境中，这里应该使用ByteBuddy或ASM进行字节码增强
        // 目前我们主要依靠CPU采样来进行性能分析
        return classfileBuffer;
    }

    /**
     * 判断是否应该忽略某个类
     */
    private boolean shouldIgnoreClass(String className) {
        if (className == null) return true;

        return className.startsWith("java/") ||
               className.startsWith("javax/") ||
               className.startsWith("sun/") ||
               className.startsWith("com/sun/") ||
               className.startsWith("jdk/") ||
               className.startsWith("com/monitor/agent/") ||
               className.startsWith("net/bytebuddy/") ||
               className.startsWith("com/fasterxml/jackson/");
    }
    
    /**
     * 方法拦截器
     * 注意：由于简化实现，这个类目前不会被实际使用
     * 在完整实现中，需要配合字节码增强技术来实现方法拦截
     */
    public static class MethodInterceptor {

        /**
         * 方法执行前的拦截逻辑
         */
        public static long onEnter(String methodSignature) {
            try {
                // 记录方法开始执行时间
                long startTime = System.nanoTime();

                // 获取当前线程ID
                long threadId = Thread.currentThread().getId();

                // 通知数据收集器方法开始执行
                DataCollector collector = DataCollectorHolder.getInstance();
                if (collector != null) {
                    collector.onMethodEnter(methodSignature, threadId, startTime);
                }

                return startTime;
            } catch (Exception e) {
                // 忽略异常，避免影响业务逻辑
                return System.nanoTime();
            }
        }

        /**
         * 方法执行后的拦截逻辑
         */
        public static void onExit(String methodSignature, long startTime, Throwable throwable) {
            try {
                long endTime = System.nanoTime();
                long duration = endTime - startTime;
                long threadId = Thread.currentThread().getId();

                // 通知数据收集器方法执行结束
                DataCollector collector = DataCollectorHolder.getInstance();
                if (collector != null) {
                    collector.onMethodExit(methodSignature, threadId, startTime, endTime, duration, throwable);
                }
            } catch (Exception e) {
                // 忽略异常，避免影响业务逻辑
            }
        }
    }
    
    /**
     * DataCollector持有者
     * 用于在静态方法中访问DataCollector实例
     */
    public static class DataCollectorHolder {
        private static volatile DataCollector instance;
        
        public static void setInstance(DataCollector collector) {
            instance = collector;
        }
        
        public static DataCollector getInstance() {
            return instance;
        }
    }
}
