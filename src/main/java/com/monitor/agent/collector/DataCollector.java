package com.monitor.agent.collector;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据收集器
 * 负责收集方法执行的性能数据
 */
public class DataCollector {
    
    private final Map<String, MethodMetrics> methodMetrics;
    private final Map<Long, ThreadMethodStack> threadStacks;
    private final AtomicLong totalMethodCalls;
    
    public DataCollector() {
        this.methodMetrics = new ConcurrentHashMap<>();
        this.threadStacks = new ConcurrentHashMap<>();
        this.totalMethodCalls = new AtomicLong(0);
        
        // 设置为全局实例，供字节码增强使用
        com.monitor.agent.instrumentation.ClassTransformer.DataCollectorHolder.setInstance(this);
    }
    
    /**
     * 方法进入时调用
     */
    public void onMethodEnter(String methodSignature, long threadId, long startTime) {
        try {
            totalMethodCalls.incrementAndGet();
            
            // 获取或创建线程方法调用栈
            ThreadMethodStack stack = threadStacks.computeIfAbsent(threadId, 
                k -> new ThreadMethodStack());
            
            // 将方法推入调用栈
            stack.push(new MethodCall(methodSignature, startTime));
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 方法退出时调用
     */
    public void onMethodExit(String methodSignature, long threadId, long startTime, 
                           long endTime, long duration, Throwable throwable) {
        try {
            // 获取线程方法调用栈
            ThreadMethodStack stack = threadStacks.get(threadId);
            if (stack != null) {
                MethodCall methodCall = stack.pop();
                if (methodCall != null && methodCall.getMethodSignature().equals(methodSignature)) {
                    // 更新方法指标
                    updateMethodMetrics(methodSignature, duration, throwable != null);
                }
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
    }
    
    /**
     * 更新方法性能指标
     */
    private void updateMethodMetrics(String methodSignature, long duration, boolean hasException) {
        MethodMetrics metrics = methodMetrics.computeIfAbsent(methodSignature, 
            k -> new MethodMetrics(methodSignature));
        
        metrics.recordExecution(duration, hasException);
    }
    
    /**
     * 获取方法性能指标
     */
    public Map<String, MethodMetrics> getMethodMetrics() {
        return new ConcurrentHashMap<>(methodMetrics);
    }
    
    /**
     * 获取总方法调用次数
     */
    public long getTotalMethodCalls() {
        return totalMethodCalls.get();
    }
    
    /**
     * 获取CPU使用率最高的方法
     */
    public java.util.List<MethodMetrics> getTopCpuMethods(int topN) {
        return methodMetrics.values().stream()
            .sorted((a, b) -> Long.compare(b.getTotalDuration(), a.getTotalDuration()))
            .limit(topN)
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 重置所有指标
     */
    public void reset() {
        methodMetrics.clear();
        threadStacks.clear();
        totalMethodCalls.set(0);
    }
    
    /**
     * 方法调用信息
     */
    private static class MethodCall {
        private final String methodSignature;
        private final long startTime;
        
        public MethodCall(String methodSignature, long startTime) {
            this.methodSignature = methodSignature;
            this.startTime = startTime;
        }
        
        public String getMethodSignature() { return methodSignature; }
        public long getStartTime() { return startTime; }
    }
    
    /**
     * 线程方法调用栈
     */
    private static class ThreadMethodStack {
        private final java.util.Stack<MethodCall> stack;
        
        public ThreadMethodStack() {
            this.stack = new java.util.Stack<>();
        }
        
        public void push(MethodCall methodCall) {
            stack.push(methodCall);
        }
        
        public MethodCall pop() {
            return stack.isEmpty() ? null : stack.pop();
        }
        
        public boolean isEmpty() {
            return stack.isEmpty();
        }
        
        public int size() {
            return stack.size();
        }
    }
    
    /**
     * 方法性能指标
     */
    public static class MethodMetrics {
        private final String methodSignature;
        private final AtomicLong callCount;
        private final AtomicLong totalDuration;
        private final AtomicLong exceptionCount;
        private volatile long minDuration;
        private volatile long maxDuration;
        private volatile double avgDuration;
        
        public MethodMetrics(String methodSignature) {
            this.methodSignature = methodSignature;
            this.callCount = new AtomicLong(0);
            this.totalDuration = new AtomicLong(0);
            this.exceptionCount = new AtomicLong(0);
            this.minDuration = Long.MAX_VALUE;
            this.maxDuration = 0;
            this.avgDuration = 0.0;
        }
        
        /**
         * 记录一次方法执行
         */
        public synchronized void recordExecution(long duration, boolean hasException) {
            long count = callCount.incrementAndGet();
            long total = totalDuration.addAndGet(duration);
            
            if (hasException) {
                exceptionCount.incrementAndGet();
            }
            
            // 更新最小值
            if (duration < minDuration) {
                minDuration = duration;
            }
            
            // 更新最大值
            if (duration > maxDuration) {
                maxDuration = duration;
            }
            
            // 更新平均值
            avgDuration = (double) total / count;
        }
        
        // Getter方法
        public String getMethodSignature() { return methodSignature; }
        public long getCallCount() { return callCount.get(); }
        public long getTotalDuration() { return totalDuration.get(); }
        public long getExceptionCount() { return exceptionCount.get(); }
        public long getMinDuration() { return minDuration == Long.MAX_VALUE ? 0 : minDuration; }
        public long getMaxDuration() { return maxDuration; }
        public double getAvgDuration() { return avgDuration; }
        
        /**
         * 获取方法的CPU使用率（基于总执行时间）
         */
        public double getCpuUsagePercent(long totalSystemTime) {
            if (totalSystemTime <= 0) return 0.0;
            return (double) totalDuration.get() / totalSystemTime * 100.0;
        }
        
        @Override
        public String toString() {
            return String.format("Method[%s] - Calls: %d, Total: %.2fms, Avg: %.2fms, Min: %.2fms, Max: %.2fms, Exceptions: %d",
                methodSignature, 
                callCount.get(),
                totalDuration.get() / 1_000_000.0,
                avgDuration / 1_000_000.0,
                getMinDuration() / 1_000_000.0,
                maxDuration / 1_000_000.0,
                exceptionCount.get());
        }
    }
}
