package com.monitor.agent;

import com.monitor.agent.collector.DataCollector;
import com.monitor.agent.instrumentation.ClassTransformer;
import com.monitor.agent.profiler.CpuProfiler;
import com.monitor.agent.reporter.ConsoleReporter;
import com.monitor.agent.reporter.ReportGenerator;

import java.lang.instrument.Instrumentation;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Java Agent主类，用于监控和分析CPU高使用率问题
 * 
 * 使用方法：
 * java -javaagent:java-cpu-monitor-agent.jar=interval=5000,threshold=80 YourMainClass
 */
public class CpuMonitorAgent {
    
    private static final String DEFAULT_INTERVAL = "5000"; // 默认采样间隔5秒
    private static final String DEFAULT_THRESHOLD = "70";  // 默认CPU阈值70%
    private static final String DEFAULT_TOP_METHODS = "10"; // 默认显示前10个热点方法
    
    private static CpuProfiler cpuProfiler;
    private static DataCollector dataCollector;
    private static ReportGenerator reportGenerator;
    private static ScheduledExecutorService scheduler;
    
    /**
     * Agent启动入口点（premain方式）
     */
    public static void premain(String agentArgs, Instrumentation inst) {
        System.out.println("[CPU Monitor Agent] Starting CPU monitoring agent...");
        startAgent(agentArgs, inst);
    }
    
    /**
     * Agent启动入口点（agentmain方式，用于动态attach）
     */
    public static void agentmain(String agentArgs, Instrumentation inst) {
        System.out.println("[CPU Monitor Agent] Attaching CPU monitoring agent...");
        startAgent(agentArgs, inst);
    }
    
    /**
     * 启动Agent的核心逻辑
     */
    private static void startAgent(String agentArgs, Instrumentation inst) {
        try {
            // 解析Agent参数
            AgentConfig config = parseAgentArgs(agentArgs);
            
            // 初始化组件
            cpuProfiler = new CpuProfiler(config.getCpuThreshold());
            dataCollector = new DataCollector();
            reportGenerator = new ReportGenerator(config.getTopMethodsCount());
            
            // 注册字节码转换器
            ClassTransformer transformer = new ClassTransformer(dataCollector);
            inst.addTransformer(transformer, true);
            
            // 启动定时监控任务
            startMonitoring(config.getSamplingInterval());
            
            // 注册JVM关闭钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                System.out.println("[CPU Monitor Agent] Shutting down...");
                shutdown();
            }));
            
            System.out.println("[CPU Monitor Agent] Agent started successfully!");
            System.out.println("[CPU Monitor Agent] Sampling interval: " + config.getSamplingInterval() + "ms");
            System.out.println("[CPU Monitor Agent] CPU threshold: " + config.getCpuThreshold() + "%");
            
        } catch (Exception e) {
            System.err.println("[CPU Monitor Agent] Failed to start agent: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 解析Agent参数
     */
    private static AgentConfig parseAgentArgs(String agentArgs) {
        AgentConfig config = new AgentConfig();
        
        if (agentArgs != null && !agentArgs.trim().isEmpty()) {
            String[] args = agentArgs.split(",");
            for (String arg : args) {
                String[] keyValue = arg.split("=");
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    
                    switch (key) {
                        case "interval":
                            config.setSamplingInterval(Long.parseLong(value));
                            break;
                        case "threshold":
                            config.setCpuThreshold(Double.parseDouble(value));
                            break;
                        case "topMethods":
                            config.setTopMethodsCount(Integer.parseInt(value));
                            break;
                    }
                }
            }
        }
        
        return config;
    }
    
    /**
     * 启动监控任务
     */
    private static void startMonitoring(long intervalMs) {
        scheduler = Executors.newScheduledThreadPool(2);
        
        // CPU监控任务
        scheduler.scheduleAtFixedRate(() -> {
            try {
                cpuProfiler.sample();
                
                // 如果CPU使用率超过阈值，生成报告
                if (cpuProfiler.isHighCpuDetected()) {
                    generateAndPrintReport();
                }
            } catch (Exception e) {
                System.err.println("[CPU Monitor Agent] Error during CPU sampling: " + e.getMessage());
            }
        }, 0, intervalMs, TimeUnit.MILLISECONDS);
        
        // 定期报告任务（每分钟）
        scheduler.scheduleAtFixedRate(() -> {
            try {
                generateAndPrintReport();
            } catch (Exception e) {
                System.err.println("[CPU Monitor Agent] Error generating report: " + e.getMessage());
            }
        }, 60000, 60000, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 生成并打印报告
     */
    private static void generateAndPrintReport() {
        String report = reportGenerator.generateReport(
            cpuProfiler.getCpuMetrics(),
            dataCollector.getMethodMetrics()
        );
        
        ConsoleReporter.printReport(report);
    }
    
    /**
     * 关闭Agent
     */
    private static void shutdown() {
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 生成最终报告
        if (reportGenerator != null && cpuProfiler != null && dataCollector != null) {
            System.out.println("\n[CPU Monitor Agent] Final Report:");
            generateAndPrintReport();
        }
    }
    
    /**
     * Agent配置类
     */
    private static class AgentConfig {
        private long samplingInterval = Long.parseLong(DEFAULT_INTERVAL);
        private double cpuThreshold = Double.parseDouble(DEFAULT_THRESHOLD);
        private int topMethodsCount = Integer.parseInt(DEFAULT_TOP_METHODS);
        
        public long getSamplingInterval() { return samplingInterval; }
        public void setSamplingInterval(long samplingInterval) { this.samplingInterval = samplingInterval; }
        
        public double getCpuThreshold() { return cpuThreshold; }
        public void setCpuThreshold(double cpuThreshold) { this.cpuThreshold = cpuThreshold; }
        
        public int getTopMethodsCount() { return topMethodsCount; }
        public void setTopMethodsCount(int topMethodsCount) { this.topMethodsCount = topMethodsCount; }
    }
}
