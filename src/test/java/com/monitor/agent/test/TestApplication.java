package com.monitor.agent.test;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 测试应用程序
 * 用于演示CPU监控Agent的功能
 */
public class TestApplication {
    
    private static final Random random = new Random();
    private static volatile boolean running = true;
    
    public static void main(String[] args) {
        System.out.println("Starting Test Application for CPU Monitor Agent...");
        System.out.println("This application will simulate various CPU-intensive operations.");
        System.out.println("Press Ctrl+C to stop the application.\n");
        
        // 注册关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("\nShutting down test application...");
            running = false;
        }));
        
        // 创建线程池
        ExecutorService executor = Executors.newFixedThreadPool(4);
        
        try {
            // 启动不同类型的CPU密集型任务
            executor.submit(new CpuIntensiveTask("MathCalculation"));
            executor.submit(new CpuIntensiveTask("StringProcessing"));
            executor.submit(new CpuIntensiveTask("ListOperations"));
            executor.submit(new CpuIntensiveTask("RecursiveCalculation"));
            
            // 主线程也执行一些任务
            runMainTask();
            
        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        System.out.println("Test application finished.");
    }
    
    /**
     * 主任务
     */
    private static void runMainTask() {
        int iteration = 0;
        while (running) {
            try {
                iteration++;
                
                // 每10次迭代执行不同的操作
                if (iteration % 10 == 0) {
                    performComplexCalculation();
                } else if (iteration % 7 == 0) {
                    performStringOperations();
                } else if (iteration % 5 == 0) {
                    performListOperations();
                } else {
                    performSimpleCalculation();
                }
                
                // 随机休眠，模拟真实应用的行为
                Thread.sleep(random.nextInt(100) + 50);
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                // 模拟异常情况
                System.err.println("Exception in main task: " + e.getMessage());
            }
        }
    }
    
    /**
     * 复杂计算任务
     */
    private static void performComplexCalculation() {
        double result = 0;
        for (int i = 0; i < 100000; i++) {
            result += Math.sin(i) * Math.cos(i) + Math.sqrt(i);
        }
        // 防止编译器优化
        if (result > Double.MAX_VALUE) {
            System.out.println("Impossible result: " + result);
        }
    }
    
    /**
     * 字符串操作任务
     */
    private static void performStringOperations() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            sb.append("String processing iteration ").append(i).append(" ");
            if (i % 100 == 0) {
                sb.toString().toUpperCase().toLowerCase();
            }
        }
        
        // 字符串查找和替换
        String text = sb.toString();
        text.replaceAll("iteration", "step");
        text.contains("processing");
    }
    
    /**
     * 列表操作任务
     */
    private static void performListOperations() {
        List<Integer> numbers = new ArrayList<>();
        
        // 添加元素
        for (int i = 0; i < 10000; i++) {
            numbers.add(random.nextInt(1000));
        }
        
        // 排序
        numbers.sort(Integer::compareTo);
        
        // 查找和过滤
        numbers.stream()
            .filter(n -> n > 500)
            .mapToInt(Integer::intValue)
            .sum();
        
        // 清空列表
        numbers.clear();
    }
    
    /**
     * 简单计算任务
     */
    private static void performSimpleCalculation() {
        int sum = 0;
        for (int i = 0; i < 10000; i++) {
            sum += i * 2;
        }
        
        // 防止编译器优化
        if (sum < 0) {
            System.out.println("Negative sum: " + sum);
        }
    }
    
    /**
     * CPU密集型任务类
     */
    private static class CpuIntensiveTask implements Runnable {
        private final String taskName;
        
        public CpuIntensiveTask(String taskName) {
            this.taskName = taskName;
        }
        
        @Override
        public void run() {
            System.out.println("Starting task: " + taskName);
            
            while (running) {
                try {
                    switch (taskName) {
                        case "MathCalculation":
                            performMathCalculations();
                            break;
                        case "StringProcessing":
                            performStringProcessing();
                            break;
                        case "ListOperations":
                            performListProcessing();
                            break;
                        case "RecursiveCalculation":
                            performRecursiveCalculation(20);
                            break;
                        default:
                            performDefaultTask();
                            break;
                    }
                    
                    // 短暂休眠
                    Thread.sleep(random.nextInt(200) + 100);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    System.err.println("Exception in task " + taskName + ": " + e.getMessage());
                }
            }
            
            System.out.println("Task finished: " + taskName);
        }
        
        private void performMathCalculations() {
            double result = 0;
            for (int i = 0; i < 50000; i++) {
                result += Math.pow(i, 2) + Math.log(i + 1) + Math.exp(i % 10);
            }
        }
        
        private void performStringProcessing() {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 5000; i++) {
                sb.append("Task ").append(taskName).append(" iteration ").append(i);
                if (i % 500 == 0) {
                    sb.toString().hashCode();
                    sb.setLength(0);
                }
            }
        }
        
        private void performListProcessing() {
            List<String> items = new ArrayList<>();
            for (int i = 0; i < 5000; i++) {
                items.add("Item-" + i + "-" + taskName);
            }
            
            // 排序和搜索
            items.sort(String::compareTo);
            items.contains("Item-2500-" + taskName);
            items.removeIf(item -> item.contains("2"));
        }
        
        private long performRecursiveCalculation(int n) {
            if (n <= 1) return 1;
            return n * performRecursiveCalculation(n - 1);
        }
        
        private void performDefaultTask() {
            for (int i = 0; i < 100000; i++) {
                Math.random();
            }
        }
    }
}
