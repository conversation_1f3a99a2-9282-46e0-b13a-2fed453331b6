# 贡献指南

感谢您对Java CPU Monitor Agent项目的关注！我们欢迎各种形式的贡献。

## 🤝 如何贡献

### 报告问题
- 使用GitHub Issues报告bug
- 提供详细的复现步骤
- 包含环境信息（Java版本、操作系统等）

### 提交代码
1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- 遵循Java编码规范
- 添加适当的注释
- 编写单元测试
- 确保所有测试通过

### 提交信息规范
```
type(scope): description

[optional body]

[optional footer]
```

类型：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式
- `refactor`: 重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📋 开发环境设置

```bash
# 克隆项目
git clone https://github.com/your-username/java-cpu-monitor-agent.git
cd java-cpu-monitor-agent

# 构建项目
./build-java11.sh

# 运行测试
mvn test

# 运行演示
./run-demo.sh
```

## 🧪 测试

请确保您的更改通过所有测试：

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify

# 测试Docker部署
docker build -f Dockerfile.with-agent -t test-agent .
```

## 📝 文档

如果您的更改影响用户界面或API，请更新相应的文档：
- README.md
- DOCKER_DEPLOYMENT_GUIDE.md
- 代码注释

## 🔍 代码审查

所有提交都需要经过代码审查。审查重点：
- 代码质量和可读性
- 性能影响
- 安全性考虑
- 向后兼容性

## 📞 联系我们

如有任何问题，请通过以下方式联系：
- GitHub Issues
- 项目讨论区

再次感谢您的贡献！
